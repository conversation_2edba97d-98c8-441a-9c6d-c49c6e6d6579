"""
Redis存储后端实现

基于aioredis的Redis存储实现，主要用于缓存和任务队列。
"""

import json
import logging
from typing import Any, Dict, List, Optional, Tuple, Union

import redis.asyncio as redis
from pydantic import Field

from .base_storage import BaseStorage, StorageConfig

logger = logging.getLogger(__name__)


class RedisConfig(StorageConfig):
    """Redis配置类"""

    host: str = Field(default="localhost")
    port: int = Field(default=6379)
    password: Optional[str] = Field(default=None)
    database: int = Field(default=0)
    max_connections: int = Field(default=100)
    socket_timeout: float = Field(default=5.0)
    socket_connect_timeout: float = Field(default=5.0)
    decode_responses: bool = Field(default=True)


class RedisStorage(BaseStorage):
    """Redis存储实现

    注意：Redis是键值存储，这里的collection参数用作键的前缀。
    """

    def __init__(self, config: RedisConfig) -> None:
        """初始化Redis存储

        Args:
            config: Redis配置
        """
        super().__init__(config)
        self.config: RedisConfig = config
        self._client: Optional[redis.Redis] = None
        self._pool: Optional[redis.ConnectionPool] = None

    async def connect(self) -> None:
        """建立Redis连接"""
        try:
            # 创建连接池
            self._pool = redis.ConnectionPool(
                host=self.config.host,
                port=self.config.port,
                password=self.config.password,
                db=self.config.database,
                max_connections=self.config.max_connections,
                socket_timeout=self.config.socket_timeout,
                socket_connect_timeout=self.config.socket_connect_timeout,
                decode_responses=self.config.decode_responses,
            )

            # 创建客户端
            self._client = redis.Redis(connection_pool=self._pool)

            # 测试连接
            await self._client.ping()
            self._connected = True

            logger.info(f"已连接到Redis: {self.config.host}:{self.config.port}")

        except Exception as e:
            logger.error(f"连接Redis失败: {e}")
            raise

    async def disconnect(self) -> None:
        """断开Redis连接"""
        if self._client:
            await self._client.close()
            self._connected = False
            logger.info("已断开Redis连接")

    async def ping(self) -> bool:
        """检查Redis连接状态"""
        try:
            if not self._client:
                return False
            await self._client.ping()
            return True
        except Exception:
            return False

    def _make_key(self, collection: str, key: str = "") -> str:
        """生成Redis键名

        Args:
            collection: 集合名（用作前缀）
            key: 键名

        Returns:
            完整的Redis键名
        """
        if key:
            return f"{collection}:{key}"
        return collection

    def _serialize_document(self, document: Dict[str, Any]) -> str:
        """序列化文档为JSON字符串

        Args:
            document: 要序列化的文档

        Returns:
            JSON字符串
        """
        return json.dumps(document, ensure_ascii=False, default=str)

    def _deserialize_document(self, data: str) -> Dict[str, Any]:
        """反序列化JSON字符串为文档

        Args:
            data: JSON字符串

        Returns:
            文档字典
        """
        return json.loads(data)  # type: ignore[no-any-return]

    async def create_collection(self, name: str, **kwargs: Any) -> bool:
        """创建Redis集合（实际上是设置一个标记键）

        Args:
            name: 集合名称
            **kwargs: 额外参数

        Returns:
            是否创建成功
        """
        try:
            if not self._client:
                raise RuntimeError("Redis未连接")

            # 在Redis中设置一个集合标记
            collection_key = self._make_key(name, "_collection_info")
            await self._client.set(
                collection_key, json.dumps({"created": True, **kwargs})
            )

            logger.info(f"已创建Redis集合: {name}")
            return True

        except Exception as e:
            logger.error(f"创建Redis集合失败: {e}")
            return False

    async def drop_collection(self, name: str) -> bool:
        """删除Redis集合（删除所有相关键）

        Args:
            name: 集合名称

        Returns:
            是否删除成功
        """
        try:
            if not self._client:
                raise RuntimeError("Redis未连接")

            # 查找所有相关键
            pattern = self._make_key(name, "*")
            keys = await self._client.keys(pattern)

            if keys:
                await self._client.delete(*keys)

            logger.info(f"已删除Redis集合: {name}")
            return True

        except Exception as e:
            logger.error(f"删除Redis集合失败: {e}")
            return False

    async def insert_one(self, collection: str, document: Dict[str, Any]) -> str:
        """插入单个文档到Redis

        Args:
            collection: 集合名称
            document: 要插入的文档

        Returns:
            插入文档的ID（Redis键）
        """
        try:
            if not self._client:
                raise RuntimeError("Redis未连接")

            # 生成唯一ID
            doc_id = document.get("_id") or await self._client.incr(
                f"{collection}:_id_counter"
            )
            key = self._make_key(collection, str(doc_id))

            # 添加ID到文档
            document["_id"] = str(doc_id)

            # 存储文档
            await self._client.set(key, self._serialize_document(document))

            # 添加到集合索引
            await self._client.sadd(f"{collection}:_keys", key)  # type: ignore[misc]

            return str(doc_id)

        except Exception as e:
            logger.error(f"插入文档失败: {e}")
            raise

    async def insert_many(
        self, collection: str, documents: List[Dict[str, Any]]
    ) -> List[str]:
        """批量插入文档到Redis

        Args:
            collection: 集合名称
            documents: 要插入的文档列表

        Returns:
            插入文档的ID列表
        """
        try:
            if not self._client:
                raise RuntimeError("Redis未连接")

            if not documents:
                return []

            ids = []
            pipe = self._client.pipeline()

            for document in documents:
                # 生成唯一ID
                doc_id = document.get("_id") or await self._client.incr(
                    f"{collection}:_id_counter"
                )
                key = self._make_key(collection, str(doc_id))

                # 添加ID到文档
                document["_id"] = str(doc_id)

                # 添加到管道
                pipe.set(key, self._serialize_document(document))
                pipe.sadd(f"{collection}:_keys", key)

                ids.append(str(doc_id))

            await pipe.execute()
            return ids

        except Exception as e:
            logger.error(f"批量插入文档失败: {e}")
            raise

    async def find_one(
        self, collection: str, filter_dict: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """从Redis查找单个文档

        Args:
            collection: 集合名称
            filter_dict: 查询条件（支持_id查询）

        Returns:
            找到的文档，如果没有找到则返回None
        """
        try:
            if not self._client:
                raise RuntimeError("Redis未连接")

            # 如果有_id过滤条件，直接查找
            if filter_dict and "_id" in filter_dict:
                key = self._make_key(collection, str(filter_dict["_id"]))
                data = await self._client.get(key)
                if data:
                    return self._deserialize_document(data)
                return None

            # 否则遍历所有键查找匹配的文档
            keys = await self._client.smembers(
                f"{collection}:_keys"
            )  # type: ignore[misc]
            for key in keys:
                data = await self._client.get(key)
                if data:
                    document = self._deserialize_document(data)
                    if self._matches_filter(document, filter_dict or {}):
                        return document

            return None

        except Exception as e:
            logger.error(f"查找文档失败: {e}")
            raise

    async def find_many(
        self,
        collection: str,
        filter_dict: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None,
        skip: Optional[int] = None,
        sort: Optional[List[Tuple[str, int]]] = None,
    ) -> List[Dict[str, Any]]:
        """从Redis查找多个文档

        Args:
            collection: 集合名称
            filter_dict: 查询条件
            limit: 限制返回数量
            skip: 跳过数量
            sort: 排序条件（Redis中排序功能有限）

        Returns:
            找到的文档列表
        """
        try:
            if not self._client:
                raise RuntimeError("Redis未连接")

            documents = []
            keys = await self._client.smembers(
                f"{collection}:_keys"
            )  # type: ignore[misc]

            for key in keys:
                data = await self._client.get(key)
                if data:
                    document = self._deserialize_document(data)
                    if self._matches_filter(document, filter_dict or {}):
                        documents.append(document)

            # 简单排序（如果指定）
            if sort:
                for field, direction in reversed(sort):
                    reverse = direction == -1
                    documents.sort(key=lambda x: x.get(field, ""), reverse=reverse)

            # 应用skip和limit
            if skip:
                documents = documents[skip:]
            if limit:
                documents = documents[:limit]

            return documents

        except Exception as e:
            logger.error(f"查找文档失败: {e}")
            raise

    def _matches_filter(
        self, document: Dict[str, Any], filter_dict: Dict[str, Any]
    ) -> bool:
        """检查文档是否匹配过滤条件

        Args:
            document: 文档
            filter_dict: 过滤条件

        Returns:
            是否匹配
        """
        for key, value in filter_dict.items():
            if key not in document or document[key] != value:
                return False
        return True

    async def update_one(
        self,
        collection: str,
        filter_dict: Dict[str, Any],
        update_dict: Dict[str, Any],
        upsert: bool = False,
    ) -> bool:
        """更新Redis中的单个文档

        Args:
            collection: 集合名称
            filter_dict: 查询条件
            update_dict: 更新内容
            upsert: 如果不存在是否插入

        Returns:
            是否更新成功
        """
        try:
            if not self._client:
                raise RuntimeError("Redis未连接")

            # 查找文档
            document = await self.find_one(collection, filter_dict)

            if document:
                # 更新文档
                if "$set" in update_dict:
                    document.update(update_dict["$set"])
                else:
                    document.update(update_dict)

                # 保存更新后的文档
                key = self._make_key(collection, str(document["_id"]))
                await self._client.set(key, self._serialize_document(document))
                return True

            elif upsert:
                # 插入新文档
                new_document = update_dict.get("$set", update_dict).copy()
                new_document.update(filter_dict)
                await self.insert_one(collection, new_document)
                return True

            return False

        except Exception as e:
            logger.error(f"更新文档失败: {e}")
            raise

    async def update_many(
        self,
        collection: str,
        filter_dict: Dict[str, Any],
        update_dict: Dict[str, Any],
    ) -> int:
        """批量更新Redis中的文档

        Args:
            collection: 集合名称
            filter_dict: 查询条件
            update_dict: 更新内容

        Returns:
            更新的文档数量
        """
        try:
            if not self._client:
                raise RuntimeError("Redis未连接")

            documents = await self.find_many(collection, filter_dict)
            updated_count = 0

            for document in documents:
                # 更新文档
                if "$set" in update_dict:
                    document.update(update_dict["$set"])
                else:
                    document.update(update_dict)

                # 保存更新后的文档
                key = self._make_key(collection, str(document["_id"]))
                await self._client.set(key, self._serialize_document(document))
                updated_count += 1

            return updated_count

        except Exception as e:
            logger.error(f"批量更新文档失败: {e}")
            raise

    async def delete_one(self, collection: str, filter_dict: Dict[str, Any]) -> bool:
        """从Redis删除单个文档

        Args:
            collection: 集合名称
            filter_dict: 查询条件

        Returns:
            是否删除成功
        """
        try:
            if not self._client:
                raise RuntimeError("Redis未连接")

            document = await self.find_one(collection, filter_dict)
            if document:
                key = self._make_key(collection, str(document["_id"]))
                await self._client.delete(key)
                await self._client.srem(
                    f"{collection}:_keys", key
                )  # type: ignore[misc]
                return True

            return False

        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            raise

    async def delete_many(self, collection: str, filter_dict: Dict[str, Any]) -> int:
        """从Redis批量删除文档

        Args:
            collection: 集合名称
            filter_dict: 查询条件

        Returns:
            删除的文档数量
        """
        try:
            if not self._client:
                raise RuntimeError("Redis未连接")

            documents = await self.find_many(collection, filter_dict)
            deleted_count = 0

            for document in documents:
                key = self._make_key(collection, str(document["_id"]))
                await self._client.delete(key)
                await self._client.srem(
                    f"{collection}:_keys", key
                )  # type: ignore[misc]
                deleted_count += 1

            return deleted_count

        except Exception as e:
            logger.error(f"批量删除文档失败: {e}")
            raise

    async def count(
        self, collection: str, filter_dict: Optional[Dict[str, Any]] = None
    ) -> int:
        """统计Redis中的文档数量

        Args:
            collection: 集合名称
            filter_dict: 查询条件

        Returns:
            文档数量
        """
        try:
            if not self._client:
                raise RuntimeError("Redis未连接")

            if not filter_dict:
                # 如果没有过滤条件，直接返回集合大小
                count = await self._client.scard(f"{collection}:_keys")
                return int(count)  # type: ignore[misc]

            # 否则需要遍历所有文档
            documents = await self.find_many(collection, filter_dict)
            return len(documents)

        except Exception as e:
            logger.error(f"统计文档数量失败: {e}")
            raise

    # Redis特有的方法

    async def set_cache(
        self, key: str, value: Union[str, Dict[str, Any]], expire: Optional[int] = None
    ) -> bool:
        """设置缓存

        Args:
            key: 缓存键
            value: 缓存值
            expire: 过期时间（秒）

        Returns:
            是否设置成功
        """
        try:
            if not self._client:
                raise RuntimeError("Redis未连接")

            if isinstance(value, dict):
                value = self._serialize_document(value)

            await self._client.set(key, value, ex=expire)
            return True

        except Exception as e:
            logger.error(f"设置缓存失败: {e}")
            return False

    async def get_cache(self, key: str) -> Optional[str]:
        """获取缓存

        Args:
            key: 缓存键

        Returns:
            缓存值
        """
        try:
            if not self._client:
                raise RuntimeError("Redis未连接")

            return await self._client.get(key)  # type: ignore[no-any-return]

        except Exception as e:
            logger.error(f"获取缓存失败: {e}")
            return None

    async def delete_cache(self, key: str) -> bool:
        """删除缓存

        Args:
            key: 缓存键

        Returns:
            是否删除成功
        """
        try:
            if not self._client:
                raise RuntimeError("Redis未连接")

            result = await self._client.delete(key)
            return result > 0  # type: ignore[no-any-return]

        except Exception as e:
            logger.error(f"删除缓存失败: {e}")
            return False
