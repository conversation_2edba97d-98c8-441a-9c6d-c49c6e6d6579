#!/bin/bash

# DataTrans 分布式数据传输系统启动脚本
# 用于启动所有Docker服务

set -e

echo "启动 DataTrans 分布式数据传输系统..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ 错误: Docker 未运行，请先启动 Docker"
    exit 1
fi

# 检查docker-compose是否可用
if ! command -v docker-compose &> /dev/null; then
    echo "❌ 错误: docker-compose 未安装"
    exit 1
fi

# 检查.env文件是否存在
if [ ! -f ".env" ]; then
    echo "❌ 错误: .env 文件不存在"
    exit 1
fi

echo "📋 检查配置文件..."

# 验证Docker Compose配置
echo "🔍 验证 Docker Compose 配置..."
if ! docker-compose config > /dev/null 2>&1; then
    echo "❌ 错误: Docker Compose 配置无效"
    exit 1
fi

echo "✅ 配置验证通过"

# 创建网络（如果不存在）
echo "🌐 创建网络..."
docker-compose up --no-start

# 启动基础设施服务（按依赖顺序）
echo "🗄️  启动数据库服务..."
docker-compose up -d postgres mongodb clickhouse

echo "⏳ 等待数据库服务启动..."
sleep 10

echo "🔄 启动缓存和消息队列服务..."
docker-compose up -d zookeeper
sleep 5
docker-compose up -d redis kafka

echo "⏳ 等待缓存和消息队列服务启动..."
sleep 10

echo "📦 启动对象存储服务..."
docker-compose up -d minio

echo "⏳ 等待对象存储服务启动..."
sleep 5

# 检查所有服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 等待所有服务健康检查通过
echo "🏥 等待健康检查..."
max_attempts=30
attempt=0

while [ $attempt -lt $max_attempts ]; do
    healthy_services=$(docker-compose ps --filter "status=running" --format "table {{.Service}}\t{{.Status}}" | grep -c "healthy" || true)
    total_services=$(docker-compose ps --filter "status=running" --format "table {{.Service}}" | wc -l)
    total_services=$((total_services - 1))  # 减去表头

    if [ "$healthy_services" -eq "$total_services" ] && [ "$total_services" -gt 0 ]; then
        echo "✅ 所有服务健康检查通过"
        break
    fi

    echo "⏳ 等待服务健康检查... ($healthy_services/$total_services 健康)"
    sleep 5
    attempt=$((attempt + 1))
done

if [ $attempt -eq $max_attempts ]; then
    echo "⚠️  警告: 部分服务可能未通过健康检查"
fi

# 显示服务访问信息
echo ""
echo "🎉 DataTrans 系统启动完成！"
echo ""
echo "📊 服务访问地址:"
echo "  PostgreSQL:     localhost:5432"
echo "  MongoDB:        localhost:27017"
echo "  ClickHouse:     localhost:8123 (HTTP), localhost:9000 (Native)"
echo "  Redis:          localhost:6379"
echo "  Kafka:          localhost:9092"
echo "  Zookeeper:      localhost:2181"
echo "  MinIO API:      localhost:9002"
echo "  MinIO Console:  localhost:9001"
echo ""
echo "🔑 默认凭据:"
echo "  PostgreSQL:     用户: data_trans_user, 密码: data_trans_password"
echo "  MongoDB:        用户: admin, 密码: admin_password"
echo "  ClickHouse:     用户: data_trans_user, 密码: data_trans_password"
echo "  MinIO:          用户: data_trans_admin, 密码: data_trans_password123"
echo ""
echo "📝 查看日志: docker-compose logs -f [服务名]"
echo "🛑 停止系统: ./scripts/stop.sh"
echo ""
