# Data Trans 项目文档

## 项目概述

Data Trans 是一个基于Python的数据处理和转换平台，支持多种数据源的采集、清洗、存储和API服务。

## 目录结构

```
data_trans/
├── src/data_trans/          # 主要源代码
│   ├── crawlers/           # 数据爬虫模块
│   ├── cleaners/           # 数据清洗模块
│   ├── storage/            # 数据存储模块
│   ├── api/                # API服务模块
│   └── config/             # 配置管理模块
├── tests/                  # 测试代码
├── docs/                   # 项目文档
├── scripts/                # 脚本文件
└── pyproject.toml          # 项目配置文件
```

## 技术栈

- **Python**: 3.11+
- **Web框架**: FastAPI
- **分布式计算**: Ray
- **数据库**: MongoDB, PostgreSQL, Redis
- **工作流**: Apache Airflow
- **包管理**: uv

## 快速开始

### 环境准备

1. 确保安装了Python 3.11+
2. 安装uv包管理器

### 安装依赖

```bash
uv sync
```

### 运行测试

```bash
uv run pytest
```

### 启动API服务

```bash
uv run python -m src.data_trans.api
```

## 开发指南

### 代码规范

项目使用以下工具确保代码质量：

- **black**: 代码格式化
- **flake8**: 代码风格检查
- **mypy**: 类型检查
- **pytest**: 单元测试

### 运行代码检查

```bash
# 格式化代码
uv run black src/ tests/

# 检查代码风格
uv run flake8 src/ tests/

# 类型检查
uv run mypy src/

# 运行测试
uv run pytest tests/
```

## 模块说明

### crawlers 模块
负责从各种数据源采集数据，支持网页爬虫、API调用等方式。

### cleaners 模块
提供数据清洗和预处理功能，包括数据验证、格式转换等。

### storage 模块
管理数据存储，支持多种数据库后端。

### api 模块
提供RESTful API服务，基于FastAPI框架。

### config 模块
统一管理项目配置，支持环境变量和配置文件。

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用MIT许可证。
