"""
数据爬虫模块

该模块包含各种数据源的爬虫实现，支持多种数据采集方式。
"""

__version__ = "0.1.0"
__author__ = "Data Trans Team"

from .api_crawler import APICrawler, APICrawlerConfig

# 导入主要的爬虫类
from .base_crawler import BaseCrawler, CrawlerConfig, CrawlerStats, CrawlResult
from .web_crawler import WebCrawler, WebCrawlerConfig

__all__: list[str] = [
    "BaseCrawler",
    "CrawlResult",
    "CrawlerConfig",
    "CrawlerStats",
    "WebCrawler",
    "WebCrawlerConfig",
    "APICrawler",
    "APICrawlerConfig",
]
