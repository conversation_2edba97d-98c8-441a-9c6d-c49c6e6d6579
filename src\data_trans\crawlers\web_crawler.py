"""
Web爬虫实现

基于httpx的异步HTTP爬虫，支持HTML解析和JavaScript渲染。
"""

import logging
import time
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin, urlparse

import httpx
from pydantic import Field

from .base_crawler import BaseCrawler, CrawlerConfig, CrawlResult

logger = logging.getLogger(__name__)


class WebCrawlerConfig(CrawlerConfig):
    """Web爬虫配置类"""

    # HTTP配置
    follow_redirects: bool = Field(default=True, description="是否跟随重定向")
    max_redirects: int = Field(default=10, description="最大重定向次数")
    verify_ssl: bool = Field(default=True, description="是否验证SSL证书")

    # 内容配置
    max_content_size: int = Field(
        default=10 * 1024 * 1024, description="最大内容大小（字节）"
    )
    allowed_content_types: List[str] = Field(
        default_factory=lambda: [
            "text/html",
            "text/plain",
            "application/json",
            "application/xml",
            "text/xml",
        ],
        description="允许的内容类型",
    )

    # JavaScript渲染配置
    js_enabled: bool = Field(default=False, description="是否启用JavaScript渲染")
    js_timeout: float = Field(default=10.0, description="JavaScript渲染超时时间")
    js_wait_for: Optional[str] = Field(default=None, description="等待特定元素出现")

    # 解析配置
    extract_links: bool = Field(default=False, description="是否提取页面链接")
    extract_images: bool = Field(default=False, description="是否提取图片链接")
    extract_text: bool = Field(default=True, description="是否提取文本内容")

    # Cookie配置
    enable_cookies: bool = Field(default=True, description="是否启用Cookie")


class WebCrawler(BaseCrawler):
    """Web爬虫实现"""

    def __init__(self, config: WebCrawlerConfig) -> None:
        """初始化Web爬虫

        Args:
            config: Web爬虫配置
        """
        super().__init__(config)
        self.config: WebCrawlerConfig = config
        self._client: Optional[httpx.AsyncClient] = None

    async def setup(self) -> None:
        """设置Web爬虫"""
        # 创建HTTP客户端
        limits = httpx.Limits(
            max_keepalive_connections=self.config.max_concurrent_requests,
            max_connections=self.config.max_concurrent_requests * 2,
        )

        timeout = httpx.Timeout(
            connect=self.config.timeout,
            read=self.config.timeout,
            write=self.config.timeout,
            pool=self.config.timeout,
        )

        self._client = httpx.AsyncClient(
            limits=limits,
            timeout=timeout,
            follow_redirects=self.config.follow_redirects,
            max_redirects=self.config.max_redirects,
            verify=self.config.verify_ssl,
            headers=self._get_headers(),
        )

        logger.info("Web爬虫已初始化")

    async def cleanup(self) -> None:
        """清理Web爬虫资源"""
        if self._client:
            await self._client.aclose()
            logger.info("Web爬虫资源已清理")

    async def fetch_single(self, url: str, **kwargs: Any) -> CrawlResult:
        """爬取单个网页

        Args:
            url: 要爬取的URL
            **kwargs: 额外参数
                - method: HTTP方法（默认GET）
                - headers: 额外请求头
                - params: URL参数
                - data: POST数据
                - json: JSON数据
                - cookies: Cookie

        Returns:
            爬取结果
        """
        if not self._client:
            raise RuntimeError("爬虫未初始化，请先调用setup()")

        start_time = time.time()
        method = kwargs.get("method", "GET").upper()
        headers = kwargs.get("headers", {})
        params = kwargs.get("params")
        data = kwargs.get("data")
        json_data = kwargs.get("json")
        cookies = kwargs.get("cookies")

        try:
            # 合并请求头
            request_headers = self._get_headers(headers)

            # 获取代理
            proxy = self._get_proxy()

            # 发送请求
            response = await self._client.request(
                method=method,
                url=url,
                headers=request_headers,
                params=params,
                data=data,
                json=json_data,
                cookies=cookies,
                proxy=proxy,  # httpx使用proxy参数，不是proxies
            )

            # 检查内容大小
            content_length = response.headers.get("content-length")
            if content_length and int(content_length) > self.config.max_content_size:
                raise ValueError(
                    f"内容大小超过限制: {content_length} > {self.config.max_content_size}"
                )

            # 检查内容类型
            content_type = (
                response.headers.get("content-type", "").split(";")[0].strip()
            )
            if content_type and content_type not in self.config.allowed_content_types:
                logger.warning(f"不支持的内容类型: {content_type}")

            # 获取响应内容
            content = response.text

            # 解析内容
            parsed_data = await self._parse_content(url, content, content_type)

            duration = time.time() - start_time

            return CrawlResult(
                url=url,
                data=parsed_data,
                status_code=response.status_code,
                headers=dict(response.headers),
                duration=duration,
            )

        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"爬取 {url} 失败: {e}")

            return CrawlResult(
                url=url, data={}, status_code=0, duration=duration, error=str(e)
            )

    async def _parse_content(
        self, url: str, content: str, content_type: str
    ) -> Dict[str, Any]:
        """解析网页内容

        Args:
            url: 页面URL
            content: 页面内容
            content_type: 内容类型

        Returns:
            解析后的数据
        """
        parsed_data: Dict[str, Any] = {
            "content_type": content_type,
            "content_length": len(content),
            "raw_content": content if len(content) <= 1000 else content[:1000] + "...",
        }

        try:
            if content_type == "application/json":
                import json

                parsed_data["json_data"] = json.loads(content)

            elif content_type in ["text/html", "application/xhtml+xml"]:
                # 解析HTML
                html_data = await self._parse_html(url, content)
                parsed_data.update(html_data)

            elif content_type in ["text/plain"]:
                parsed_data["text_content"] = content

            elif content_type in ["application/xml", "text/xml"]:
                # 简单的XML解析
                parsed_data["xml_content"] = content

        except Exception as e:
            logger.warning(f"解析内容失败: {e}")
            parsed_data["parse_error"] = str(e)

        return parsed_data

    async def _parse_html(self, url: str, html_content: str) -> Dict[str, Any]:
        """解析HTML内容

        Args:
            url: 页面URL
            html_content: HTML内容

        Returns:
            解析后的HTML数据
        """
        html_data: Dict[str, Any] = {}

        try:
            # 这里可以使用BeautifulSoup或其他HTML解析库
            # 为了减少依赖，这里使用简单的正则表达式
            import re

            # 提取标题
            title_match = re.search(
                r"<title[^>]*>(.*?)</title>", html_content, re.IGNORECASE | re.DOTALL
            )
            if title_match:
                html_data["title"] = title_match.group(1).strip()

            # 提取meta描述
            desc_match = re.search(
                r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']*)["\']',
                html_content,
                re.IGNORECASE,
            )
            if desc_match:
                html_data["description"] = desc_match.group(1)

            # 提取meta关键词
            keywords_match = re.search(
                r'<meta[^>]*name=["\']keywords["\'][^>]*content=["\']([^"\']*)["\']',
                html_content,
                re.IGNORECASE,
            )
            if keywords_match:
                html_data["keywords"] = keywords_match.group(1)

            # 提取链接
            if self.config.extract_links:
                links = self._extract_links(url, html_content)
                html_data["links"] = links

            # 提取图片
            if self.config.extract_images:
                images = self._extract_images(url, html_content)
                html_data["images"] = images

            # 提取文本内容
            if self.config.extract_text:
                text_content = self._extract_text(html_content)
                html_data["text_content"] = text_content

        except Exception as e:
            logger.warning(f"解析HTML失败: {e}")
            html_data["parse_error"] = str(e)

        return html_data

    def _extract_links(self, base_url: str, html_content: str) -> List[Dict[str, str]]:
        """提取页面链接

        Args:
            base_url: 基础URL
            html_content: HTML内容

        Returns:
            链接列表
        """
        import re

        links = []
        link_pattern = r'<a[^>]*href=["\']([^"\']*)["\'][^>]*>(.*?)</a>'

        for match in re.finditer(link_pattern, html_content, re.IGNORECASE | re.DOTALL):
            href = match.group(1).strip()
            text = re.sub(r"<[^>]+>", "", match.group(2)).strip()

            # 转换为绝对URL
            absolute_url = urljoin(base_url, href)

            links.append({"url": absolute_url, "text": text, "href": href})

        return links

    def _extract_images(self, base_url: str, html_content: str) -> List[Dict[str, str]]:
        """提取页面图片

        Args:
            base_url: 基础URL
            html_content: HTML内容

        Returns:
            图片列表
        """
        import re

        images = []
        img_pattern = (
            r'<img[^>]*src=["\']([^"\']*)["\'][^>]*(?:alt=["\']([^"\']*)["\'])?[^>]*>'
        )

        for match in re.finditer(img_pattern, html_content, re.IGNORECASE):
            src = match.group(1).strip()
            alt = match.group(2) or ""

            # 转换为绝对URL
            absolute_url = urljoin(base_url, src)

            images.append({"url": absolute_url, "alt": alt, "src": src})

        return images

    def _extract_text(self, html_content: str) -> str:
        """提取HTML文本内容

        Args:
            html_content: HTML内容

        Returns:
            纯文本内容
        """
        import re

        # 移除脚本和样式标签
        html_content = re.sub(
            r"<script[^>]*>.*?</script>",
            "",
            html_content,
            flags=re.IGNORECASE | re.DOTALL,
        )
        html_content = re.sub(
            r"<style[^>]*>.*?</style>",
            "",
            html_content,
            flags=re.IGNORECASE | re.DOTALL,
        )

        # 移除HTML标签
        text = re.sub(r"<[^>]+>", "", html_content)

        # 清理空白字符
        text = re.sub(r"\s+", " ", text).strip()

        return text

    async def crawl_sitemap(self, sitemap_url: str) -> List[str]:
        """爬取站点地图获取URL列表

        Args:
            sitemap_url: 站点地图URL

        Returns:
            URL列表
        """
        try:
            result = await self.fetch_single(sitemap_url)

            if result.error or result.status_code != 200:
                logger.error(f"获取站点地图失败: {result.error}")
                return []

            content = result.data.get("raw_content", "")
            urls = self._parse_sitemap(content)

            logger.info(f"从站点地图获取到 {len(urls)} 个URL")
            return urls

        except Exception as e:
            logger.error(f"爬取站点地图失败: {e}")
            return []

    def _parse_sitemap(self, sitemap_content: str) -> List[str]:
        """解析站点地图内容

        Args:
            sitemap_content: 站点地图内容

        Returns:
            URL列表
        """
        import re

        urls = []

        # XML格式的sitemap
        url_pattern = r"<loc>(.*?)</loc>"
        for match in re.finditer(url_pattern, sitemap_content, re.IGNORECASE):
            url = match.group(1).strip()
            if url:
                urls.append(url)

        # 如果没有找到XML格式，尝试文本格式
        if not urls:
            lines = sitemap_content.split("\n")
            for line in lines:
                line = line.strip()
                if line and line.startswith("http"):
                    urls.append(line)

        return urls

    async def check_robots_txt(self, base_url: str) -> Dict[str, Any]:
        """检查robots.txt文件

        Args:
            base_url: 网站基础URL

        Returns:
            robots.txt信息
        """
        try:
            parsed_url = urlparse(base_url)
            robots_url = f"{parsed_url.scheme}://{parsed_url.netloc}/robots.txt"

            result = await self.fetch_single(robots_url)

            if result.error or result.status_code != 200:
                return {"exists": False, "error": result.error}

            content = result.data.get("raw_content", "")
            robots_info = self._parse_robots_txt(content)
            robots_info["exists"] = True

            return robots_info

        except Exception as e:
            logger.error(f"检查robots.txt失败: {e}")
            return {"exists": False, "error": str(e)}

    def _parse_robots_txt(self, robots_content: str) -> Dict[str, Any]:
        """解析robots.txt内容

        Args:
            robots_content: robots.txt内容

        Returns:
            解析后的robots信息
        """
        robots_info: Dict[str, Any] = {
            "user_agents": {},
            "sitemaps": [],
            "crawl_delay": None,
        }

        current_user_agent = "*"

        for line in robots_content.split("\n"):
            line = line.strip()
            if not line or line.startswith("#"):
                continue

            if ":" in line:
                key, value = line.split(":", 1)
                key = key.strip().lower()
                value = value.strip()

                if key == "user-agent":
                    current_user_agent = value
                    if current_user_agent not in robots_info["user_agents"]:
                        robots_info["user_agents"][current_user_agent] = {
                            "disallow": [],
                            "allow": [],
                        }

                elif key == "disallow":
                    if current_user_agent not in robots_info["user_agents"]:
                        robots_info["user_agents"][current_user_agent] = {
                            "disallow": [],
                            "allow": [],
                        }
                    robots_info["user_agents"][current_user_agent]["disallow"].append(
                        value
                    )

                elif key == "allow":
                    if current_user_agent not in robots_info["user_agents"]:
                        robots_info["user_agents"][current_user_agent] = {
                            "disallow": [],
                            "allow": [],
                        }
                    robots_info["user_agents"][current_user_agent]["allow"].append(
                        value
                    )

                elif key == "sitemap":
                    robots_info["sitemaps"].append(value)

                elif key == "crawl-delay":
                    try:
                        robots_info["crawl_delay"] = float(value)
                    except ValueError:
                        pass

        return robots_info
