# Pre-commit hooks配置
repos:
  # Python代码格式化
  - repo: https://github.com/psf/black
    rev: 25.1.0
    hooks:
      - id: black
        language_version: python3.11
        args: [--line-length=88]

  # Python导入排序
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: ["--profile", "black", "--filter-files"]

  # Python代码检查
  - repo: https://github.com/pycqa/flake8
    rev: 7.3.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203]

  # Python类型检查
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.16.1
    hooks:
      - id: mypy
        additional_dependencies: [pydantic>=2.5.0, types-redis, types-requests]
        args: [--strict, --ignore-missing-imports, --explicit-package-bases]

  # 通用文件检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      # 检查文件末尾是否有换行符
      - id: end-of-file-fixer
      # 移除行尾空白字符
      - id: trailing-whitespace
      # 检查YAML文件语法
      - id: check-yaml
      # 检查JSON文件语法
      - id: check-json
      # 检查TOML文件语法
      - id: check-toml
      # 检查合并冲突标记
      - id: check-merge-conflict
      # 检查大文件
      - id: check-added-large-files
        args: ['--maxkb=1000']
      # 检查Python AST语法
      - id: check-ast
      # 检查docstring第一行
      - id: check-docstring-first

  # 安全检查
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.10
    hooks:
      - id: bandit
        args: ["-r", "src/"]
        exclude: tests/

# 配置选项
default_language_version:
  python: python3.11

# CI配置
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
