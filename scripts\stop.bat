@echo off
REM DataTrans 分布式数据传输系统停止脚本 (Windows)
REM 用于优雅停止所有Docker服务

echo 🛑 停止 DataTrans 分布式数据传输系统...

REM 检查Docker是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: Docker 未运行
    pause
    exit /b 1
)

REM 检查是否有运行的服务
docker-compose ps -q >nul 2>&1
if errorlevel 1 (
    echo ℹ️  没有运行的服务
    pause
    exit /b 0
)

echo 📋 当前运行的服务:
docker-compose ps

echo.
echo 请选择停止选项:
echo 1. 停止所有服务 (保留数据)
echo 2. 停止所有服务并删除容器 (保留数据)
echo 3. 完全清理 (删除容器、网络、匿名卷)
echo 4. 危险清理 (删除所有内容包括数据卷)
echo 5. 取消
echo.

set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" goto stop_services
if "%choice%"=="2" goto stop_and_remove
if "%choice%"=="3" goto full_cleanup
if "%choice%"=="4" goto dangerous_cleanup
if "%choice%"=="5" goto cancel
echo ❌ 无效选择
pause
exit /b 1

:stop_services
echo 🔄 停止所有服务...
docker-compose stop
echo ✅ 所有服务已停止
goto end

:stop_and_remove
echo 🔄 停止服务并删除容器...
docker-compose down
echo ✅ 服务已停止，容器已删除
goto end

:full_cleanup
echo ⚠️  警告: 这将删除容器、网络和匿名卷
set /p confirm="确认继续? (y/N): "
if /i not "%confirm%"=="y" goto cancel
echo 🧹 执行完全清理...
docker-compose down --volumes --remove-orphans
echo ✅ 完全清理完成
goto end

:dangerous_cleanup
echo ⚠️  危险警告: 这将删除所有内容包括数据卷！
echo 🚨 所有数据库数据、文件存储将被永久删除！
echo.
set /p confirm1="确认删除所有数据? (yes/no): "
if /i not "%confirm1%"=="yes" goto cancel
echo.
echo 🚨 最后确认: 输入 'DELETE_ALL_DATA' 以确认删除所有数据
set /p confirm2="确认输入: "
if not "%confirm2%"=="DELETE_ALL_DATA" goto cancel
echo 💥 执行危险清理...
docker-compose down --volumes --remove-orphans
docker volume prune -f
echo ✅ 危险清理完成 - 所有数据已删除
goto end

:cancel
echo ❌ 操作已取消
goto end

:end
echo.
echo 📊 剩余Docker资源:
echo.
echo 🐳 容器:
docker ps -a --filter "name=data_trans"
echo.
echo 📦 卷:
docker volume ls --filter "name=data_trans"
echo.
echo 🌐 网络:
docker network ls --filter "name=data_trans"
echo.
echo 🔄 重新启动系统: scripts\start.bat
echo 📝 查看日志: docker-compose logs [服务名]
echo.
pause
