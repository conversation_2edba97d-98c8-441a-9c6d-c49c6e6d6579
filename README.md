# DataTrans - 分布式数据采集、清洗和存储系统

一个基于Python 3.11+的现代化分布式数据处理系统，支持数据采集、清洗、存储和API服务。

## 🚀 项目特性

### 核心功能
- **分布式数据采集**: 支持Web爬虫和API数据采集
- **智能数据清洗**: 文本处理和数据验证
- **多存储后端**: MongoDB、Redis、PostgreSQL支持
- **RESTful API**: 完整的API接口和文档
- **分布式计算**: 基于Ray的分布式处理能力
- **现代化架构**: 异步编程、类型提示、配置管理

### 技术栈
- **Python 3.11+** - 现代Python特性
- **FastAPI** - 高性能Web框架
- **Pydantic v2** - 数据验证和设置管理
- **Motor** - 异步MongoDB驱动
- **aioredis** - 异步Redis客户端
- **Ray** - 分布式计算框架
- **httpx** - 现代HTTP客户端

## 📁 项目结构

```
data_trans/
├── src/data_trans/           # 主要源代码
│   ├── api/                  # API模块
│   │   ├── app.py           # FastAPI应用
│   │   ├── middleware.py    # 中间件
│   │   └── routes/          # API路由
│   │       ├── crawlers.py  # 爬虫API
│   │       ├── cleaners.py  # 清洗API
│   │       └── storage.py   # 存储API
│   ├── crawlers/            # 数据采集模块
│   │   ├── base_crawler.py  # 基础爬虫类
│   │   ├── web_crawler.py   # Web爬虫
│   │   └── api_crawler.py   # API爬虫
│   ├── cleaners/            # 数据清洗模块
│   │   ├── base_cleaner.py  # 基础清洗类
│   │   ├── text_cleaner.py  # 文本清洗
│   │   └── data_validator.py # 数据验证
│   ├── storage/             # 存储模块
│   │   ├── base_storage.py  # 基础存储类
│   │   ├── mongodb_storage.py # MongoDB存储
│   │   └── redis_storage.py # Redis存储
│   ├── config/              # 配置管理
│   │   └── settings.py      # 应用配置
│   └── __main__.py          # 主入口点
├── pyproject.toml           # 项目配置
└── test_api.py             # API测试脚本
```

## 🛠️ 安装和设置

### 环境要求
- Python 3.11+
- uv (推荐) 或 pip

### 安装依赖
```bash
# 使用uv (推荐)
uv sync

# 或使用pip
pip install -e .
```

### 环境配置
创建 `.env` 文件配置环境变量：

```env
# 应用配置
APP_NAME=DataTrans
APP_VERSION=0.1.0
ENVIRONMENT=development
DEBUG=true

# 服务配置
HOST=0.0.0.0
PORT=8000

# MongoDB配置
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DATABASE=data_trans

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DATABASE=0

# 爬虫配置
CRAWLER_MAX_CONCURRENT=10
CRAWLER_RATE_LIMIT=1.0
```

## 🚀 使用方法

### 启动API服务器
```bash
# 使用uv
uv run python -m src.data_trans api

# 使用默认配置启动
uv run python -m src.data_trans api --host 0.0.0.0 --port 8000

# 开发模式（自动重载）
uv run python -m src.data_trans api --reload
```

### 查看系统状态
```bash
uv run python -m src.data_trans status
```

### 启动工作进程
```bash
# 爬虫工作进程
uv run python -m src.data_trans crawler

# 清洗工作进程
uv run python -m src.data_trans cleaner

# 分布式集群
uv run python -m src.data_trans cluster
```

## 📚 API文档

启动服务器后，访问以下地址查看API文档：

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

### 主要API端点

#### 爬虫管理
- `GET /crawlers/` - 获取爬虫列表
- `POST /crawlers/` - 创建爬虫任务
- `GET /crawlers/{crawler_id}` - 获取爬虫详情
- `POST /crawlers/{crawler_id}/start` - 启动爬虫
- `POST /crawlers/{crawler_id}/stop` - 停止爬虫

#### 数据清洗
- `GET /cleaners/` - 获取清洗器列表
- `POST /cleaners/clean` - 执行数据清洗
- `POST /cleaners/validate` - 数据验证
- `GET /cleaners/tasks` - 获取清洗任务

#### 数据存储
- `POST /storage/documents` - 创建文档
- `GET /storage/documents/{doc_id}` - 获取文档
- `PUT /storage/documents/{doc_id}` - 更新文档
- `DELETE /storage/documents/{doc_id}` - 删除文档
- `GET /storage/collections` - 获取集合列表

## 🧪 测试

### 运行API测试
```bash
# 确保API服务器正在运行，然后执行：
uv run python test_api.py
```

### 手动测试
```bash
# 健康检查
curl http://localhost:8000/health

# 获取爬虫列表
curl http://localhost:8000/crawlers/

# 获取系统状态
curl http://localhost:8000/
```

## 🔧 开发

### 代码格式化
```bash
uv run black src/ tests/
uv run isort src/ tests/
```

### 类型检查
```bash
uv run mypy src/
```

### 运行测试
```bash
uv run pytest
```

## 📋 当前状态

### ✅ 已完成功能
- [x] 项目结构和配置管理
- [x] 基础存储模块（MongoDB、Redis）
- [x] 爬虫模块（Web、API）
- [x] 数据清洗模块（文本处理、验证）
- [x] FastAPI应用和中间件
- [x] 完整的API路由和端点
- [x] 命令行接口
- [x] 配置管理和环境变量支持
- [x] 类型提示和文档

### 🚧 待实现功能
- [ ] Ray分布式计算集成
- [ ] 实际的数据库操作实现
- [ ] 任务队列和调度
- [ ] 监控和日志系统
- [ ] 单元测试和集成测试
- [ ] Docker容器化
- [ ] 部署配置

### ⚠️ 已知问题
- 部分API端点返回模拟数据
- 需要实际的数据库连接配置
- 分布式功能需要进一步开发

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

## 📄 许可证

本项目采用MIT许可证。
