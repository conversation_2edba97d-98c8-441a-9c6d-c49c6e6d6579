# 网络配置
networks:
  dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  # Redis数据卷
  redis-data:
    driver: local

  # MongoDB数据卷
  mongo-data:
    driver: local

  # PostgreSQL数据卷
  postgres-data:
    driver: local

  # Kafka数据卷
  kafka-data:
    driver: local

  # MinIO对象存储数据卷
  minio-data:
    driver: local

  # ClickHouse数据卷
  clickhouse-data:
    driver: local

# 服务配置
services:
  # PostgreSQL 15+ 数据库服务
  postgres:
    image: postgres:15
    container_name: data_trans_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: data_trans
      POSTGRES_USER: data_trans_user
      POSTGRES_PASSWORD: data_trans_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    networks:
      - dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U data_trans_user -d data_trans"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # MongoDB 6.0+ 文档数据库服务
  mongodb:
    image: mongo:6.0
    container_name: data_trans_mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin_password
      MONGO_INITDB_DATABASE: data_trans
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db
      - ./docker/mongodb/init:/docker-entrypoint-initdb.d
    networks:
      - dev-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # ClickHouse 23.0+ OLAP分析数据库服务
  clickhouse:
    image: clickhouse/clickhouse-server:23
    container_name: data_trans_clickhouse
    restart: unless-stopped
    environment:
      CLICKHOUSE_DB: data_trans
      CLICKHOUSE_USER: data_trans_user
      CLICKHOUSE_PASSWORD: data_trans_password
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: 1
    ports:
      - "8123:8123"  # HTTP接口
      - "9000:9000"  # Native接口
    volumes:
      - clickhouse-data:/var/lib/clickhouse
      - ./docker/clickhouse/config:/etc/clickhouse-server/config.d
      - ./docker/clickhouse/users:/etc/clickhouse-server/users.d
    networks:
      - dev-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    ulimits:
      nofile:
        soft: 262144
        hard: 262144

  # Redis 7.0+ 缓存服务
  redis:
    image: redis:7-alpine
    container_name: data_trans_redis
    restart: unless-stopped
    command: redis-server /etc/redis/redis.conf
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
      - ./docker/redis/config:/etc/redis
    networks:
      - dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Zookeeper 服务 (Kafka依赖)
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: data_trans_zookeeper
    restart: unless-stopped
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_SYNC_LIMIT: 2
    ports:
      - "2181:2181"
    volumes:
      - ./docker/zookeeper/data:/var/lib/zookeeper/data
      - ./docker/zookeeper/logs:/var/lib/zookeeper/log
    networks:
      - dev-network
    healthcheck:
      test: ["CMD", "bash", "-c", "echo 'ruok' | nc localhost 2181"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Apache Kafka 3.5+ 消息队列服务
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: data_trans_kafka
    restart: unless-stopped
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
      KAFKA_NUM_PARTITIONS: 3
      KAFKA_DEFAULT_REPLICATION_FACTOR: 1
      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_RETENTION_BYTES: **********
      KAFKA_LOG_SEGMENT_BYTES: **********
      KAFKA_MESSAGE_MAX_BYTES: 1000000
    ports:
      - "9092:9092"
    volumes:
      - kafka-data:/var/lib/kafka/data
    networks:
      - dev-network
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # MinIO 对象存储服务
  minio:
    image: minio/minio:latest
    container_name: data_trans_minio
    restart: unless-stopped
    environment:
      MINIO_ROOT_USER: data_trans_admin
      MINIO_ROOT_PASSWORD: data_trans_password123
      MINIO_BROWSER_REDIRECT_URL: http://localhost:9001
    command: server /data --console-address ":9001"
    ports:
      - "9002:9000"  # API端口
      - "9001:9001"  # 控制台端口
    volumes:
      - minio-data:/data
      - ./docker/minio/config:/root/.minio
    networks:
      - dev-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
