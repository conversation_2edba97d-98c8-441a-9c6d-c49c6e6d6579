#!/usr/bin/env python3
"""
项目设置脚本

用于初始化项目环境和配置。
"""

import sys
from pathlib import Path


def setup_environment() -> None:
    """设置项目环境"""
    print("正在设置Data Trans项目环境...")

    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    print(f"项目根目录: {project_root}")

    # 检查Python版本
    if sys.version_info < (3, 11):
        print("错误: 需要Python 3.11或更高版本")
        sys.exit(1)

    print(f"Python版本: {sys.version}")

    # 检查uv是否安装
    try:
        import subprocess  # nosec B404

        result = subprocess.run(
            ["uv", "--version"], capture_output=True, text=True
        )  # nosec B603 B607
        if result.returncode == 0:
            print(f"uv版本: {result.stdout.strip()}")
        else:
            print("警告: uv未安装或不可用")
    except FileNotFoundError:
        print("警告: uv未安装")

    print("环境设置完成!")


def create_env_file() -> None:
    """创建环境变量文件模板"""
    env_file = Path(__file__).parent.parent / ".env"

    if not env_file.exists():
        env_content = """# Data Trans 环境变量配置

# 数据库配置
MONGODB_URL=mongodb://localhost:27017
POSTGRESQL_URL=postgresql://user:password@localhost:5432/data_trans
REDIS_URL=redis://localhost:6379

# API配置
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=true

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/data_trans.log

# 其他配置
SECRET_KEY=your-secret-key-here
"""

        with open(env_file, "w", encoding="utf-8") as f:
            f.write(env_content)

        print(f"已创建环境变量文件: {env_file}")
    else:
        print("环境变量文件已存在")


if __name__ == "__main__":
    setup_environment()
    create_env_file()
