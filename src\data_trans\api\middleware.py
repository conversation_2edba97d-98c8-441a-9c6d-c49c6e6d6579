"""
API中间件

包含各种中间件的实现，如日志记录、错误处理、认证等。
"""

import logging
import time
from typing import Any, Callable, Dict, cast

from fastapi import FastAPI, Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """日志记录中间件"""

    async def dispatch(
        self, request: Request, call_next: Callable[..., Any]
    ) -> Response:
        """处理请求并记录日志

        Args:
            request: HTTP请求
            call_next: 下一个中间件或路由处理器

        Returns:
            HTTP响应
        """
        start_time = time.time()

        # 记录请求信息
        logger.info(
            f"请求开始: {request.method} {request.url.path} "
            f"来源IP: {request.client.host if request.client else 'unknown'}"
        )

        # 处理请求
        response = await call_next(request)

        # 计算处理时间
        process_time = time.time() - start_time

        # 记录响应信息
        logger.info(
            f"请求完成: {request.method} {request.url.path} "
            f"状态码: {response.status_code} "
            f"耗时: {process_time:.3f}s"
        )

        # 添加处理时间到响应头
        response.headers["X-Process-Time"] = str(process_time)

        return cast(Response, response)


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """错误处理中间件"""

    async def dispatch(
        self, request: Request, call_next: Callable[..., Any]
    ) -> Response:
        """处理请求并捕获异常

        Args:
            request: HTTP请求
            call_next: 下一个中间件或路由处理器

        Returns:
            HTTP响应
        """
        try:
            response = await call_next(request)
            return cast(Response, response)

        except Exception as e:
            logger.error(
                f"请求处理异常: {request.method} {request.url.path} - {str(e)}"
            )

            # 返回统一的错误响应
            return JSONResponse(
                status_code=500,
                content={
                    "error": "内部服务器错误",
                    "message": "请求处理时发生异常",
                    "detail": str(e) if logger.level <= logging.DEBUG else None,
                    "path": str(request.url.path),
                    "method": request.method,
                },
            )


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""

    async def dispatch(
        self, request: Request, call_next: Callable[..., Any]
    ) -> Response:
        """添加安全响应头

        Args:
            request: HTTP请求
            call_next: 下一个中间件或路由处理器

        Returns:
            HTTP响应
        """
        response = await call_next(request)

        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self'; "
            "connect-src 'self'"
        )

        return cast(Response, response)


class RateLimitMiddleware(BaseHTTPMiddleware):
    """简单的速率限制中间件"""

    def __init__(self, app: FastAPI, calls: int = 100, period: int = 60):
        """初始化速率限制中间件

        Args:
            app: FastAPI应用
            calls: 允许的调用次数
            period: 时间窗口（秒）
        """
        super().__init__(app)
        self.calls = calls
        self.period = period
        self.clients: Dict[str, list[float]] = {}  # 存储客户端请求记录

    async def dispatch(
        self, request: Request, call_next: Callable[..., Any]
    ) -> Response:
        """检查速率限制

        Args:
            request: HTTP请求
            call_next: 下一个中间件或路由处理器

        Returns:
            HTTP响应
        """
        client_ip = request.client.host if request.client else "unknown"
        current_time = time.time()

        # 清理过期记录
        self._cleanup_expired_records(current_time)

        # 检查客户端请求记录
        if client_ip not in self.clients:
            self.clients[client_ip] = []

        client_requests = self.clients[client_ip]

        # 过滤时间窗口内的请求
        recent_requests = [
            req_time
            for req_time in client_requests
            if current_time - req_time < self.period
        ]

        # 检查是否超过限制
        if len(recent_requests) >= self.calls:
            logger.warning(f"客户端 {client_ip} 超过速率限制")
            return JSONResponse(
                status_code=429,
                content={
                    "error": "请求过于频繁",
                    "message": f"每{self.period}秒最多允许{self.calls}次请求",
                    "retry_after": self.period,
                },
                headers={"Retry-After": str(self.period)},
            )

        # 记录当前请求
        recent_requests.append(current_time)
        self.clients[client_ip] = recent_requests

        return cast(Response, await call_next(request))

    def _cleanup_expired_records(self, current_time: float) -> None:
        """清理过期的请求记录

        Args:
            current_time: 当前时间戳
        """
        expired_clients = []

        for client_ip, requests in self.clients.items():
            # 过滤掉过期的请求
            recent_requests = [
                req_time
                for req_time in requests
                if current_time - req_time < self.period
            ]

            if recent_requests:
                self.clients[client_ip] = recent_requests
            else:
                expired_clients.append(client_ip)

        # 删除没有近期请求的客户端记录
        for client_ip in expired_clients:
            del self.clients[client_ip]


def setup_middleware(app: FastAPI) -> None:
    """设置所有中间件

    Args:
        app: FastAPI应用实例
    """
    # 添加中间件（注意顺序很重要）

    # 1. 安全头中间件（最外层）
    app.add_middleware(SecurityHeadersMiddleware)

    # 2. 速率限制中间件
    app.add_middleware(RateLimitMiddleware, calls=100, period=60)

    # 3. 错误处理中间件
    app.add_middleware(ErrorHandlingMiddleware)

    # 4. 日志记录中间件（最内层，记录所有请求）
    app.add_middleware(LoggingMiddleware)

    logger.info("中间件设置完成")


# 异常处理器
async def validation_exception_handler(
    request: Request, exc: Exception
) -> JSONResponse:
    """验证异常处理器

    Args:
        request: HTTP请求
        exc: 异常对象

    Returns:
        JSON错误响应
    """
    return JSONResponse(
        status_code=422,
        content={
            "error": "数据验证错误",
            "message": "请求数据格式不正确",
            "detail": str(exc),
            "path": str(request.url.path),
        },
    )


async def http_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """HTTP异常处理器

    Args:
        request: HTTP请求
        exc: 异常对象

    Returns:
        JSON错误响应
    """
    status_code = getattr(exc, "status_code", 500)
    detail = getattr(exc, "detail", str(exc))

    return JSONResponse(
        status_code=status_code,
        content={
            "error": "HTTP错误",
            "message": detail,
            "path": str(request.url.path),
            "status_code": status_code,
        },
    )
