"""
配置管理模块

提供统一的配置管理接口，支持环境变量和配置文件。
充分利用Python 3.11的新特性进行类型安全和性能优化。
"""

from .settings import (
    AppConfig,
    CrawlerConfig,
    DatabaseConfig,
    Environment,
    LogLevel,
    RedisConfig,
    get_settings,
    reload_settings,
    settings,
)

__all__ = [
    "AppConfig",
    "CrawlerConfig",
    "DatabaseConfig",
    "Environment",
    "LogLevel",
    "RedisConfig",
    "get_settings",
    "reload_settings",
    "settings",
]

# 版本信息
__version__ = "0.1.0"
