"""
数据存储模块

该模块包含各种数据存储后端的实现，支持多种数据库和存储系统。
"""

__version__ = "0.1.0"
__author__ = "Data Trans Team"

# 导入主要的存储类
from .base_storage import BaseStorage, StorageConfig
from .mongodb_storage import MongoDBConfig, MongoDBStorage
from .redis_storage import RedisConfig, RedisStorage

__all__: list[str] = [
    "BaseStorage",
    "StorageConfig",
    "MongoDBStorage",
    "MongoDBConfig",
    "RedisStorage",
    "RedisConfig",
]
