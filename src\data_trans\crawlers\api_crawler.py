"""
API爬虫实现

专门用于爬取REST API和GraphQL接口的爬虫。
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional

import httpx
from pydantic import Field

from .base_crawler import BaseCrawler, CrawlerConfig, CrawlResult

logger = logging.getLogger(__name__)


class APICrawlerConfig(CrawlerConfig):
    """API爬虫配置类"""

    # API认证配置
    auth_type: Optional[str] = Field(
        default=None, description="认证类型: bearer, basic, api_key"
    )
    auth_token: Optional[str] = Field(default=None, description="认证令牌")
    auth_username: Optional[str] = Field(
        default=None, description="用户名（Basic认证）"
    )
    auth_password: Optional[str] = Field(default=None, description="密码（Basic认证）")
    api_key_header: str = Field(default="X-API-Key", description="API密钥头名称")

    # API特定配置
    default_format: str = Field(default="json", description="默认数据格式")
    pagination_enabled: bool = Field(default=False, description="是否启用分页")
    pagination_param: str = Field(default="page", description="分页参数名")
    pagination_size_param: str = Field(default="limit", description="分页大小参数名")
    max_pages: int = Field(default=100, description="最大分页数")

    # GraphQL配置
    graphql_endpoint: Optional[str] = Field(default=None, description="GraphQL端点")

    # 数据验证
    validate_json: bool = Field(default=True, description="是否验证JSON格式")
    required_fields: List[str] = Field(default_factory=list, description="必需字段列表")


class APICrawler(BaseCrawler):
    """API爬虫实现"""

    def __init__(self, config: APICrawlerConfig) -> None:
        """初始化API爬虫

        Args:
            config: API爬虫配置
        """
        super().__init__(config)
        self.config: APICrawlerConfig = config
        self._client: Optional[httpx.AsyncClient] = None

    async def setup(self) -> None:
        """设置API爬虫"""
        # 创建HTTP客户端
        limits = httpx.Limits(
            max_keepalive_connections=self.config.max_concurrent_requests,
            max_connections=self.config.max_concurrent_requests * 2,
        )

        timeout = httpx.Timeout(
            connect=self.config.timeout,
            read=self.config.timeout,
            write=self.config.timeout,
            pool=self.config.timeout,
        )

        # 设置认证头
        headers = self._get_auth_headers()

        self._client = httpx.AsyncClient(
            limits=limits, timeout=timeout, headers=headers, follow_redirects=True
        )

        logger.info("API爬虫已初始化")

    async def cleanup(self) -> None:
        """清理API爬虫资源"""
        if self._client:
            await self._client.aclose()
            logger.info("API爬虫资源已清理")

    def _get_auth_headers(self) -> Dict[str, str]:
        """获取认证头

        Returns:
            认证头字典
        """
        headers = self._get_headers()

        if self.config.auth_type == "bearer" and self.config.auth_token:
            headers["Authorization"] = f"Bearer {self.config.auth_token}"

        elif self.config.auth_type == "api_key" and self.config.auth_token:
            headers[self.config.api_key_header] = self.config.auth_token

        elif (
            self.config.auth_type == "basic"
            and self.config.auth_username
            and self.config.auth_password
        ):
            import base64

            credentials = f"{self.config.auth_username}:{self.config.auth_password}"
            encoded_credentials = base64.b64encode(credentials.encode()).decode()
            headers["Authorization"] = f"Basic {encoded_credentials}"

        # 设置默认Content-Type
        if "Content-Type" not in headers:
            headers["Content-Type"] = "application/json"

        return headers

    async def fetch_single(self, url: str, **kwargs: Any) -> CrawlResult:
        """爬取单个API端点

        Args:
            url: API端点URL
            **kwargs: 额外参数
                - method: HTTP方法（默认GET）
                - headers: 额外请求头
                - params: URL参数
                - data: 请求数据
                - json: JSON数据
                - auth: 认证信息

        Returns:
            爬取结果
        """
        if not self._client:
            raise RuntimeError("爬虫未初始化，请先调用setup()")

        start_time = time.time()
        method = kwargs.get("method", "GET").upper()
        headers = kwargs.get("headers", {})
        params = kwargs.get("params")
        data = kwargs.get("data")
        json_data = kwargs.get("json")
        auth = kwargs.get("auth")

        try:
            # 合并请求头
            request_headers = self._get_auth_headers()
            request_headers.update(headers)

            # 获取代理
            proxy = self._get_proxy()

            # 发送请求
            response = await self._client.request(
                method=method,
                url=url,
                headers=request_headers,
                params=params,
                data=data,
                json=json_data,
                auth=auth,
                proxy=proxy,  # httpx使用proxy参数，不是proxies
            )

            # 解析响应数据
            parsed_data = await self._parse_api_response(response)

            duration = time.time() - start_time

            return CrawlResult(
                url=url,
                data=parsed_data,
                status_code=response.status_code,
                headers=dict(response.headers),
                duration=duration,
            )

        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"爬取API {url} 失败: {e}")

            return CrawlResult(
                url=url, data={}, status_code=0, duration=duration, error=str(e)
            )

    async def _parse_api_response(self, response: httpx.Response) -> Dict[str, Any]:
        """解析API响应

        Args:
            response: HTTP响应对象

        Returns:
            解析后的数据
        """
        parsed_data: Dict[str, Any] = {
            "content_type": response.headers.get("content-type", ""),
            "content_length": len(response.content),
        }

        try:
            content_type = response.headers.get("content-type", "").lower()

            if "application/json" in content_type:
                json_data = response.json()
                parsed_data["json_data"] = json_data
                parsed_data["format"] = "json"

                # 验证JSON数据
                if self.config.validate_json:
                    validation_result = self._validate_json_data(json_data)
                    parsed_data["validation"] = validation_result

            elif "application/xml" in content_type or "text/xml" in content_type:
                parsed_data["xml_data"] = response.text
                parsed_data["format"] = "xml"

            elif "text/plain" in content_type:
                parsed_data["text_data"] = response.text
                parsed_data["format"] = "text"

            elif "text/csv" in content_type:
                parsed_data["csv_data"] = response.text
                parsed_data["format"] = "csv"

            else:
                # 尝试解析为JSON
                try:
                    json_data = response.json()
                    parsed_data["json_data"] = json_data
                    parsed_data["format"] = "json"
                except Exception:
                    parsed_data["raw_data"] = response.text
                    parsed_data["format"] = "unknown"

        except Exception as e:
            logger.warning(f"解析API响应失败: {e}")
            parsed_data["parse_error"] = str(e)
            parsed_data["raw_data"] = response.text

        return parsed_data

    def _validate_json_data(self, json_data: Any) -> Dict[str, Any]:
        """验证JSON数据

        Args:
            json_data: JSON数据

        Returns:
            验证结果
        """
        validation_result = {"is_valid": True, "errors": [], "warnings": []}

        try:
            # 检查必需字段
            if self.config.required_fields and isinstance(json_data, dict):
                for field in self.config.required_fields:
                    if field not in json_data:
                        if isinstance(validation_result["errors"], list):
                            validation_result["errors"].append(f"缺少必需字段: {field}")
                        validation_result["is_valid"] = False

            # 检查数据类型
            if isinstance(json_data, dict):
                validation_result["data_type"] = "object"
                validation_result["field_count"] = len(json_data)
            elif isinstance(json_data, list):
                validation_result["data_type"] = "array"
                validation_result["item_count"] = len(json_data)
            else:
                validation_result["data_type"] = type(json_data).__name__

        except Exception as e:
            if isinstance(validation_result["errors"], list):
                validation_result["errors"].append(f"验证过程出错: {e}")
            validation_result["is_valid"] = False

        return validation_result

    async def fetch_paginated(self, base_url: str, **kwargs: Any) -> List[CrawlResult]:
        """爬取分页API数据

        Args:
            base_url: 基础API URL
            **kwargs: 额外参数
                - page_size: 每页大小
                - start_page: 起始页码
                - max_pages: 最大页数

        Returns:
            所有页面的爬取结果列表
        """
        if not self.config.pagination_enabled:
            logger.warning("分页功能未启用")
            return [await self.fetch_single(base_url, **kwargs)]

        page_size = kwargs.pop("page_size", 20)
        start_page = kwargs.pop("start_page", 1)
        max_pages = kwargs.pop("max_pages", self.config.max_pages)

        results = []
        current_page = start_page

        while current_page <= start_page + max_pages - 1:
            # 构建分页参数
            params = kwargs.get("params", {}).copy()
            params[self.config.pagination_param] = current_page
            params[self.config.pagination_size_param] = page_size

            # 更新kwargs
            page_kwargs = kwargs.copy()
            page_kwargs["params"] = params

            # 爬取当前页
            result = await self.fetch_single(base_url, **page_kwargs)
            results.append(result)

            # 检查是否还有更多数据
            if not self._has_more_pages(result):
                break

            current_page += 1

            # 添加延迟避免过快请求
            await asyncio.sleep(0.1)

        logger.info(f"分页爬取完成，共 {len(results)} 页")
        return results

    def _has_more_pages(self, result: CrawlResult) -> bool:
        """检查是否还有更多页面

        Args:
            result: 当前页面的爬取结果

        Returns:
            是否还有更多页面
        """
        if result.error or result.status_code != 200:
            return False

        json_data = result.data.get("json_data")
        if not isinstance(json_data, dict):
            return False

        # 检查常见的分页指示字段
        pagination_indicators = [
            "has_more",
            "hasMore",
            "has_next",
            "hasNext",
            "next_page",
            "nextPage",
            "next",
            "more",
        ]

        for indicator in pagination_indicators:
            if indicator in json_data:
                return bool(json_data[indicator])

        # 检查数据数组长度
        data_fields = ["data", "items", "results", "records"]
        for field in data_fields:
            if field in json_data and isinstance(json_data[field], list):
                # 如果返回的数据少于请求的数量，可能没有更多数据了
                return len(json_data[field]) > 0

        return False

    async def fetch_graphql(
        self, query: str, variables: Optional[Dict[str, Any]] = None, **kwargs: Any
    ) -> CrawlResult:
        """爬取GraphQL接口

        Args:
            query: GraphQL查询语句
            variables: 查询变量
            **kwargs: 额外参数

        Returns:
            爬取结果
        """
        if not self.config.graphql_endpoint:
            raise ValueError("未配置GraphQL端点")

        # 构建GraphQL请求数据
        graphql_data: Dict[str, Any] = {"query": query}

        if variables:
            graphql_data["variables"] = variables

        # 发送POST请求
        return await self.fetch_single(
            self.config.graphql_endpoint, method="POST", json=graphql_data, **kwargs
        )

    async def test_endpoint(self, url: str) -> Dict[str, Any]:
        """测试API端点可用性

        Args:
            url: API端点URL

        Returns:
            测试结果
        """
        test_result = {
            "url": url,
            "is_available": False,
            "response_time": 0.0,
            "status_code": 0,
            "error": None,
        }

        try:
            start_time = time.time()
            result = await self.fetch_single(url, method="HEAD")
            test_result["response_time"] = time.time() - start_time
            test_result["status_code"] = result.status_code
            test_result["is_available"] = 200 <= result.status_code < 400
            test_result["error"] = result.error

        except Exception as e:
            test_result["error"] = str(e)

        return test_result

    async def discover_endpoints(self, base_url: str) -> List[str]:
        """发现API端点

        Args:
            base_url: 基础URL

        Returns:
            发现的端点列表
        """
        endpoints = []

        # 常见的API文档端点
        doc_endpoints = [
            "/api/docs",
            "/docs",
            "/swagger",
            "/api/swagger",
            "/openapi.json",
            "/api/openapi.json",
            "/api-docs",
            "/redoc",
        ]

        for endpoint in doc_endpoints:
            try:
                full_url = base_url.rstrip("/") + endpoint
                result = await self.fetch_single(full_url)

                if result.status_code == 200 and not result.error:
                    endpoints.append(full_url)

                    # 如果是OpenAPI/Swagger文档，尝试解析端点
                    if "openapi" in endpoint or "swagger" in endpoint:
                        parsed_endpoints = self._parse_openapi_spec(result.data)
                        endpoints.extend(parsed_endpoints)

            except Exception as e:
                logger.debug(f"检查端点 {endpoint} 失败: {e}")

        return endpoints

    def _parse_openapi_spec(self, api_data: Dict[str, Any]) -> List[str]:
        """解析OpenAPI规范获取端点

        Args:
            api_data: API文档数据

        Returns:
            端点列表
        """
        endpoints = []

        try:
            json_data = api_data.get("json_data", {})
            paths = json_data.get("paths", {})

            for path in paths.keys():
                endpoints.append(path)

        except Exception as e:
            logger.warning(f"解析OpenAPI规范失败: {e}")

        return endpoints

    async def batch_test_endpoints(
        self, endpoints: List[str]
    ) -> Dict[str, Dict[str, Any]]:
        """批量测试API端点

        Args:
            endpoints: 端点列表

        Returns:
            测试结果字典
        """
        tasks = []
        for endpoint in endpoints:
            task = asyncio.create_task(self.test_endpoint(endpoint))
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        test_results = {}
        for i, result in enumerate(results):
            endpoint = endpoints[i]
            if isinstance(result, Exception):
                test_results[endpoint] = {"is_available": False, "error": str(result)}
            elif isinstance(result, dict):
                test_results[endpoint] = result

        return test_results
