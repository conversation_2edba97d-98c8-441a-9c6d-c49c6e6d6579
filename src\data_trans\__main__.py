"""
DataTrans主应用入口点

提供命令行接口来启动应用的不同组件。
"""

import argparse
import logging
import sys
from typing import Optional

import uvicorn

from .config.settings import get_settings

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def run_api_server(
    host: Optional[str] = None,
    port: Optional[int] = None,
    reload: Optional[bool] = None,
    workers: Optional[int] = None,
) -> None:
    """启动API服务器

    Args:
        host: 服务器主机地址
        port: 服务器端口
        reload: 是否启用自动重载
        workers: 工作进程数
    """
    settings = get_settings()

    # 使用传入的参数或配置文件中的默认值
    server_host = host or settings.host
    server_port = port or settings.port
    server_reload = reload if reload is not None else settings.debug
    server_workers = workers or 1

    logger.info(f"启动API服务器: {server_host}:{server_port}")
    logger.info(f"调试模式: {server_reload}")
    logger.info(f"工作进程数: {server_workers}")

    # 启动服务器
    uvicorn.run(
        "data_trans.api.app:app",
        host=server_host,
        port=server_port,
        reload=server_reload,
        workers=(
            server_workers if not server_reload else 1
        ),  # reload模式下只能使用1个worker
        log_level="info",
        access_log=True,
    )


def run_crawler_worker() -> None:
    """启动爬虫工作进程"""
    logger.info("启动爬虫工作进程...")

    # 这里应该实现爬虫工作进程的逻辑
    # 例如：监听任务队列、执行爬取任务等

    logger.warning("爬虫工作进程功能尚未实现")


def run_cleaner_worker() -> None:
    """启动清洗工作进程"""
    logger.info("启动清洗工作进程...")

    # 这里应该实现清洗工作进程的逻辑
    # 例如：监听清洗队列、执行数据清洗等

    logger.warning("清洗工作进程功能尚未实现")


def run_distributed_cluster() -> None:
    """启动分布式集群"""
    logger.info("启动分布式集群...")

    try:
        import ray

        # 初始化Ray集群
        ray.init()
        logger.info("Ray集群已初始化")

        # 这里应该实现分布式任务调度逻辑
        logger.warning("分布式集群功能尚未完全实现")

        # 保持集群运行
        try:
            import time

            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("正在关闭分布式集群...")
            ray.shutdown()

    except ImportError:
        logger.error("Ray未安装，无法启动分布式集群")
        sys.exit(1)


def show_status() -> None:
    """显示系统状态"""
    settings = get_settings()

    print("=== DataTrans 系统状态 ===")
    print(f"应用名称: {settings.app_name}")
    print(f"版本: {settings.app_version}")
    print(f"环境: {settings.environment.value}")
    print(f"调试模式: {settings.debug}")
    print(f"API地址: http://{settings.host}:{settings.port}")
    print(f"MongoDB: {settings.database.mongodb_host}:{settings.database.mongodb_port}")
    print(f"Redis: {settings.redis.host}:{settings.redis.port}")
    print("========================")


def main() -> None:
    """主函数"""
    parser = argparse.ArgumentParser(
        description="DataTrans - 分布式数据收集、清洗和存储系统"
    )

    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # API服务器命令
    api_parser = subparsers.add_parser("api", help="启动API服务器")
    api_parser.add_argument("--host", type=str, help="服务器主机地址")
    api_parser.add_argument("--port", type=int, help="服务器端口")
    api_parser.add_argument("--reload", action="store_true", help="启用自动重载")
    api_parser.add_argument("--workers", type=int, help="工作进程数")

    # 工作进程命令
    subparsers.add_parser("crawler", help="启动爬虫工作进程")
    subparsers.add_parser("cleaner", help="启动清洗工作进程")

    # 分布式集群命令
    subparsers.add_parser("cluster", help="启动分布式集群")

    # 状态命令
    subparsers.add_parser("status", help="显示系统状态")

    # 解析参数
    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    try:
        if args.command == "api":
            run_api_server(
                host=args.host, port=args.port, reload=args.reload, workers=args.workers
            )
        elif args.command == "crawler":
            run_crawler_worker()
        elif args.command == "cleaner":
            run_cleaner_worker()
        elif args.command == "cluster":
            run_distributed_cluster()
        elif args.command == "status":
            show_status()
        else:
            parser.print_help()

    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
