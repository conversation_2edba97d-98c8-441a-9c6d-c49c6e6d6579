#!/usr/bin/env python3
"""
DataTrans API 基本功能测试脚本
"""

import asyncio

import httpx


async def test_api_endpoints() -> None:
    """测试API端点的基本功能"""
    base_url = "http://localhost:8000"

    async with httpx.AsyncClient(timeout=30.0) as client:
        print("开始测试 DataTrans API...")

        # 测试根端点
        print("\n📍 测试根端点...")
        try:
            response = await client.get(f"{base_url}/")
            print(f"✅ 根端点响应: {response.status_code}")
            if response.status_code == 200:
                print(f"   响应内容: {response.json()}")
        except Exception as e:
            print(f"❌ 根端点测试失败: {e}")

        # 测试健康检查
        print("\n🏥 测试健康检查...")
        try:
            response = await client.get(f"{base_url}/health")
            print(f"✅ 健康检查响应: {response.status_code}")
            if response.status_code == 200:
                print(f"   响应内容: {response.json()}")
        except Exception as e:
            print(f"❌ 健康检查测试失败: {e}")

        # 测试API文档
        print("\n📚 测试API文档...")
        try:
            response = await client.get(f"{base_url}/docs")
            print(f"✅ API文档响应: {response.status_code}")
        except Exception as e:
            print(f"❌ API文档测试失败: {e}")

        # 测试爬虫端点
        print("\n🕷️ 测试爬虫端点...")
        try:
            response = await client.get(f"{base_url}/crawlers/")
            print(f"✅ 爬虫列表响应: {response.status_code}")
            if response.status_code == 200:
                print(f"   响应内容: {response.json()}")
        except Exception as e:
            print(f"❌ 爬虫端点测试失败: {e}")

        # 测试清洗端点
        print("\n🧹 测试清洗端点...")
        try:
            response = await client.get(f"{base_url}/cleaners/")
            print(f"✅ 清洗器列表响应: {response.status_code}")
            if response.status_code == 200:
                print(f"   响应内容: {response.json()}")
        except Exception as e:
            print(f"❌ 清洗端点测试失败: {e}")

        # 测试存储端点
        print("\n💾 测试存储端点...")
        try:
            response = await client.get(f"{base_url}/storage/collections")
            print(f"✅ 存储集合响应: {response.status_code}")
            if response.status_code == 200:
                print(f"   响应内容: {response.json()}")
        except Exception as e:
            print(f"❌ 存储端点测试失败: {e}")

        print("\n🎉 API测试完成!")


def main() -> None:
    """主函数"""
    print("DataTrans API 测试工具")
    print("=" * 50)

    try:
        asyncio.run(test_api_endpoints())
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
