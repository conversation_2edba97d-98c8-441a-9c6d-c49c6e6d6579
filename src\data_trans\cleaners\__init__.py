"""
数据清洗模块

该模块包含各种数据清洗和预处理功能，支持多种数据格式的清洗操作。
"""

__version__ = "0.1.0"
__author__ = "Data Trans Team"

# 导入主要的数据清洗类
from .base_cleaner import BaseCleaner, CleanerConfig, CleaningResult, CleaningRule
from .data_validator import DataValidator, ValidationResult, ValidationRule
from .text_cleaner import TextCleaner, TextCleanerConfig

__all__: list[str] = [
    "BaseCleaner",
    "CleanerConfig",
    "CleaningResult",
    "CleaningRule",
    "TextCleaner",
    "TextCleanerConfig",
    "DataValidator",
    "ValidationRule",
    "ValidationResult",
]
