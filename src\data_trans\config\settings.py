"""
统一配置管理模块
支持环境变量和配置文件，充分利用Python 3.11的新特性
"""

from enum import Enum
from pathlib import Path
from typing import List, Optional, Union

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Environment(str, Enum):
    """环境类型枚举"""

    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class LogLevel(str, Enum):
    """日志级别枚举"""

    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class DatabaseConfig(BaseSettings):
    """数据库配置类"""

    # PostgreSQL配置
    postgres_host: str = Field(default="localhost", alias="POSTGRES_HOST")
    postgres_port: int = Field(default=5432, alias="POSTGRES_PORT")
    postgres_user: str = Field(default="postgres", alias="POSTGRES_USER")
    postgres_password: str = Field(default="password", alias="POSTGRES_PASSWORD")
    postgres_database: str = Field(default="data_trans", alias="POSTGRES_DATABASE")
    postgres_pool_size: int = Field(default=10, alias="POSTGRES_POOL_SIZE")
    postgres_max_overflow: int = Field(default=20, alias="POSTGRES_MAX_OVERFLOW")

    # MongoDB配置
    mongodb_host: str = Field(default="localhost", alias="MONGODB_HOST")
    mongodb_port: int = Field(default=27017, alias="MONGODB_PORT")
    mongodb_user: Optional[str] = Field(default=None, alias="MONGODB_USER")
    mongodb_password: Optional[str] = Field(default=None, alias="MONGODB_PASSWORD")
    mongodb_database: str = Field(default="data_trans", alias="MONGODB_DATABASE")
    mongodb_auth_source: str = Field(default="admin", alias="MONGODB_AUTH_SOURCE")

    # ClickHouse配置
    clickhouse_host: str = Field(default="localhost", alias="CLICKHOUSE_HOST")
    clickhouse_port: int = Field(default=9000, alias="CLICKHOUSE_PORT")
    clickhouse_user: str = Field(default="default", alias="CLICKHOUSE_USER")
    clickhouse_password: str = Field(default="", alias="CLICKHOUSE_PASSWORD")
    clickhouse_database: str = Field(default="data_trans", alias="CLICKHOUSE_DATABASE")

    @property
    def postgres_url(self) -> str:
        """PostgreSQL连接URL"""
        return (
            f"postgresql://{self.postgres_user}:{self.postgres_password}"
            f"@{self.postgres_host}:{self.postgres_port}/{self.postgres_database}"
        )

    @property
    def mongodb_url(self) -> str:
        """MongoDB连接URL"""
        if self.mongodb_user and self.mongodb_password:
            return (
                f"mongodb://{self.mongodb_user}:{self.mongodb_password}"
                f"@{self.mongodb_host}:{self.mongodb_port}/{self.mongodb_database}"
                f"?authSource={self.mongodb_auth_source}"
            )
        return (
            f"mongodb://{self.mongodb_host}:{self.mongodb_port}/{self.mongodb_database}"
        )

    @property
    def clickhouse_url(self) -> str:
        """ClickHouse连接URL"""
        return (
            f"clickhouse://{self.clickhouse_user}:{self.clickhouse_password}"
            f"@{self.clickhouse_host}:{self.clickhouse_port}/{self.clickhouse_database}"
        )

    model_config = SettingsConfigDict(env_prefix="DB_", case_sensitive=False)


class RedisConfig(BaseSettings):
    """Redis配置类"""

    host: str = Field(default="localhost", alias="REDIS_HOST")
    port: int = Field(default=6379, alias="REDIS_PORT")
    password: Optional[str] = Field(default=None, alias="REDIS_PASSWORD")
    database: int = Field(default=0, alias="REDIS_DATABASE")
    max_connections: int = Field(default=100, alias="REDIS_MAX_CONNECTIONS")
    socket_timeout: float = Field(default=5.0, alias="REDIS_SOCKET_TIMEOUT")
    socket_connect_timeout: float = Field(default=5.0, alias="REDIS_CONNECT_TIMEOUT")

    # 任务队列配置
    queue_database: int = Field(default=1, alias="REDIS_QUEUE_DATABASE")
    result_database: int = Field(default=2, alias="REDIS_RESULT_DATABASE")

    @property
    def url(self) -> str:
        """Redis连接URL"""
        auth = f":{self.password}@" if self.password else ""
        return f"redis://{auth}{self.host}:{self.port}/{self.database}"

    @property
    def queue_url(self) -> str:
        """任务队列Redis连接URL"""
        auth = f":{self.password}@" if self.password else ""
        return f"redis://{auth}{self.host}:{self.port}/{self.queue_database}"

    @property
    def result_url(self) -> str:
        """结果存储Redis连接URL"""
        auth = f":{self.password}@" if self.password else ""
        return f"redis://{auth}{self.host}:{self.port}/{self.result_database}"

    model_config = SettingsConfigDict(env_prefix="REDIS_", case_sensitive=False)


class CrawlerConfig(BaseSettings):
    """爬虫配置类"""

    # 基础配置
    user_agent: str = Field(
        default="DataTrans/1.0 (+https://github.com/datatrans/crawler)",
        alias="CRAWLER_USER_AGENT",
    )
    timeout: float = Field(default=30.0, alias="CRAWLER_TIMEOUT")
    max_retries: int = Field(default=3, alias="CRAWLER_MAX_RETRIES")
    retry_delay: float = Field(default=1.0, alias="CRAWLER_RETRY_DELAY")

    # 并发控制
    max_concurrent_requests: int = Field(default=10, alias="CRAWLER_MAX_CONCURRENT")
    rate_limit_per_second: float = Field(default=1.0, alias="CRAWLER_RATE_LIMIT")

    # 代理配置
    proxy_enabled: bool = Field(default=False, alias="CRAWLER_PROXY_ENABLED")
    proxy_list: List[str] = Field(default_factory=list, alias="CRAWLER_PROXY_LIST")
    proxy_rotation: bool = Field(default=True, alias="CRAWLER_PROXY_ROTATION")

    # JavaScript渲染
    js_enabled: bool = Field(default=False, alias="CRAWLER_JS_ENABLED")
    js_timeout: float = Field(default=10.0, alias="CRAWLER_JS_TIMEOUT")

    # 存储配置
    download_path: Path = Field(
        default=Path("./downloads"), alias="CRAWLER_DOWNLOAD_PATH"
    )

    @field_validator("proxy_list", mode="before")
    @classmethod
    def parse_proxy_list(cls, v: Union[str, List[str]]) -> List[str]:
        """解析代理列表"""
        if isinstance(v, str):
            return [proxy.strip() for proxy in v.split(",") if proxy.strip()]
        return v if isinstance(v, list) else []

    @field_validator("download_path", mode="before")
    @classmethod
    def parse_download_path(cls, v: Union[str, Path]) -> Path:
        """解析下载路径"""
        if isinstance(v, str):
            return Path(v)
        return v if isinstance(v, Path) else Path("./downloads")

    model_config = SettingsConfigDict(env_prefix="CRAWLER_", case_sensitive=False)


class AppConfig(BaseSettings):
    """应用主配置类"""

    # 基础配置
    app_name: str = Field(default="DataTrans", alias="APP_NAME")
    app_version: str = Field(default="0.1.0", alias="APP_VERSION")
    environment: Environment = Field(
        default=Environment.DEVELOPMENT, alias="ENVIRONMENT"
    )
    debug: bool = Field(default=True, alias="DEBUG")

    # 服务配置
    host: str = Field(default="0.0.0.0", alias="HOST")  # nosec B104
    port: int = Field(default=8000, alias="PORT")
    workers: int = Field(default=1, alias="WORKERS")

    # 安全配置
    secret_key: str = Field(default="your-secret-key-here", alias="SECRET_KEY")
    access_token_expire_minutes: int = Field(
        default=30, alias="ACCESS_TOKEN_EXPIRE_MINUTES"
    )

    # 日志配置
    log_level: LogLevel = Field(default=LogLevel.INFO, alias="LOG_LEVEL")
    log_file: Optional[Path] = Field(default=None, alias="LOG_FILE")

    # Ray配置
    ray_address: Optional[str] = Field(default=None, alias="RAY_ADDRESS")
    ray_num_cpus: Optional[int] = Field(default=None, alias="RAY_NUM_CPUS")
    ray_num_gpus: Optional[int] = Field(default=None, alias="RAY_NUM_GPUS")

    # 子配置
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    redis: RedisConfig = Field(default_factory=RedisConfig)
    crawler: CrawlerConfig = Field(default_factory=CrawlerConfig)

    @field_validator("environment", mode="before")
    @classmethod
    def parse_environment(cls, v: Union[str, Environment]) -> Environment:
        """解析环境变量"""
        if isinstance(v, str):
            return Environment(v.lower())
        return v  # type: ignore[unreachable]

    @field_validator("log_level", mode="before")
    @classmethod
    def parse_log_level(cls, v: Union[str, LogLevel]) -> LogLevel:
        """解析日志级别"""
        if isinstance(v, str):
            return LogLevel(v.upper())
        return v  # type: ignore[unreachable]

    @field_validator("log_file", mode="before")
    @classmethod
    def parse_log_file(cls, v: Union[str, Path, None]) -> Optional[Path]:
        """解析日志文件路径"""
        if v is None:
            return None
        if isinstance(v, str):
            return Path(v)
        return v if isinstance(v, Path) else None

    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.environment == Environment.DEVELOPMENT

    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.environment == Environment.PRODUCTION

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", case_sensitive=False, extra="ignore"
    )


# 全局配置实例
settings = AppConfig()


def get_settings() -> AppConfig:
    """获取配置实例"""
    return settings


def reload_settings() -> AppConfig:
    """重新加载配置"""
    global settings
    settings = AppConfig()
    return settings
