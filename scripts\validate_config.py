#!/usr/bin/env python3
"""
配置验证脚本
验证项目配置与Python 3.11的兼容性
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    from data_trans.config import get_settings

    print("✅ 配置模块导入成功")
except ImportError as e:
    print(f"❌ 配置模块导入失败: {e}")
    sys.exit(1)


def validate_python_version() -> bool:
    """验证Python版本"""
    if sys.version_info < (3, 11):
        print(f"❌ Python版本过低: {sys.version}")
        print("需要Python 3.11或更高版本")
        return False
    else:
        print(f"✅ Python版本: {sys.version}")
        return True


def validate_config_loading() -> bool:
    """验证配置加载"""
    try:
        config = get_settings()
        print("✅ 配置加载成功")

        # 验证基础配置
        print(f"  - 应用名称: {config.app_name}")
        print(f"  - 环境: {config.environment}")
        print(f"  - 调试模式: {config.debug}")

        # 验证数据库配置
        print(f"  - PostgreSQL URL: {config.database.postgres_url}")
        print(f"  - MongoDB URL: {config.database.mongodb_url}")
        print(f"  - Redis URL: {config.redis.url}")

        return True
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False


def validate_type_hints() -> bool:
    """验证类型提示"""
    try:
        from typing import get_type_hints

        from data_trans.config.settings import AppConfig

        hints = get_type_hints(AppConfig)
        print("✅ 类型提示验证成功")
        print(f"  - 发现 {len(hints)} 个类型提示")

        return True
    except Exception as e:
        print(f"❌ 类型提示验证失败: {e}")
        return False


def main() -> int:
    """主函数"""
    print("开始验证Python 3.11兼容性...")
    print("=" * 50)

    checks = [
        ("Python版本检查", validate_python_version),
        ("配置加载检查", validate_config_loading),
        ("类型提示检查", validate_type_hints),
    ]

    passed = 0
    total = len(checks)

    for name, check_func in checks:
        print(f"\n{name}:")
        if check_func():
            passed += 1
        else:
            print(f"检查失败: {name}")

    print("\n" + "=" * 50)
    print(f"验证结果: {passed}/{total} 项检查通过")

    if passed == total:
        print("🎉 所有检查通过！项目与Python 3.11完全兼容")
        return 0
    else:
        print("⚠️  部分检查失败，请修复后重试")
        return 1


if __name__ == "__main__":
    sys.exit(main())
