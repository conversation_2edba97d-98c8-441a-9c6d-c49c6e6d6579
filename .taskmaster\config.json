{"models": {"main": {"provider": "openrouter", "modelId": "anthropic/claude-sonnet-4", "maxTokens": 120000, "temperature": 0.2}, "research": {"provider": "openrouter", "modelId": "anthropic/claude-sonnet-4", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "openrouter", "modelId": "anthropic/claude-sonnet-4", "maxTokens": 120000, "temperature": 0.2}}, "global": {"logLevel": "info", "debug": false, "defaultNumTasks": 10, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "responseLanguage": "chinese", "defaultTag": "master", "azureOpenaiBaseURL": "https://your-endpoint.openai.azure.com/", "userId": "**********"}, "claudeCode": {}}