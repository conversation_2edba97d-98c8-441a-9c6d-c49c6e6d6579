[project]
name = "data-trans"
version = "0.1.0"
description = "分布式数据采集、清洗和存储系统"
readme = "README.md"
authors = [
    {name = "Data Trans Team", email = "<EMAIL>"}
]
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "ray[default]>=2.8.0",
    "redis>=5.0.0",
    "aioredis>=2.0.0",
    "pymongo>=4.6.0",
    "motor>=3.3.0",
    "psycopg2-binary>=2.9.0",
    "apache-airflow>=2.7.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "httpx>=0.25.0",
    "aiofiles>=23.2.0",
    "python-multipart>=0.0.6",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
    "slowapi>=0.1.9",
]

[project.optional-dependencies]
dev = [
    "pytest",
    "black",
    "flake8",
    "mypy",
    "pre-commit",
]

[tool.hatch.build.targets.wheel]
packages = ["src/data_trans"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[dependency-groups]
dev = [
    "black>=25.1.0",
    "flake8>=7.3.0",
    "mypy>=1.16.1",
    "pre-commit>=4.2.0",
    "pytest>=8.4.1",
]

# Black代码格式化配置
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort导入排序配置
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
src_paths = ["src", "tests"]
skip_glob = ["*/migrations/*"]
known_first_party = ["data_trans"]
known_third_party = [
    "fastapi",
    "ray",
    "redis",
    "pymongo",
    "psycopg2",
    "airflow",
    "pytest",
]

# Flake8代码检查配置
[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "docs/source/conf.py",
    "old",
    "build",
    "dist",
    ".venv",
    ".eggs",
    "*.egg",
]

# MyPy类型检查配置
[tool.mypy]
python_version = "3.11"
warn_return_any = false
warn_unused_configs = false
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = false
disallow_untyped_decorators = false
no_implicit_optional = false
warn_redundant_casts = false
warn_unused_ignores = false
warn_no_return = false
warn_unreachable = false
strict_equality = false
show_error_codes = false
# Pydantic v2 plugin
plugins = ["pydantic.mypy"]
ignore_errors = true

[[tool.mypy.overrides]]
module = [
    "ray.*",
    "redis.*",
    "pymongo.*",
    "psycopg2.*",
    "airflow.*",
    "pydantic_settings.*",
    "motor.*",
    "aioredis.*",
    "httpx.*",
    "beautifulsoup4.*",
    "lxml.*",
    "slowapi.*",
    "starlette.*",
    "uvicorn.*",
    "aiofiles.*",
    "fastapi.*",
]
ignore_missing_imports = true
ignore_errors = true

[tool.pydantic-mypy]
init_forbid_extra = true
init_typed = true
warn_required_dynamic_aliases = true

# Pytest配置
[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers"
testpaths = [
    "tests",
]
python_files = [
    "test_*.py",
    "*_test.py",
]
python_classes = [
    "Test*",
]
python_functions = [
    "test_*",
]

# Coverage配置
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
