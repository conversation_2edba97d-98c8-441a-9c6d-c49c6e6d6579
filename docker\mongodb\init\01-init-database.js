// MongoDB初始化脚本
// 创建数据传输系统所需的集合和索引

// 切换到data_trans数据库
db = db.getSiblingDB('data_trans');

// 创建用户
db.createUser({
  user: 'data_trans_user',
  pwd: 'data_trans_password',
  roles: [
    {
      role: 'readWrite',
      db: 'data_trans'
    }
  ]
});

// 创建原始数据集合
db.createCollection('raw_data', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['source_id', 'data', 'created_at'],
      properties: {
        source_id: {
          bsonType: 'string',
          description: '数据源ID'
        },
        task_id: {
          bsonType: 'string',
          description: '任务ID'
        },
        data: {
          bsonType: 'object',
          description: '原始数据内容'
        },
        metadata: {
          bsonType: 'object',
          description: '元数据信息'
        },
        created_at: {
          bsonType: 'date',
          description: '创建时间'
        }
      }
    }
  }
});

// 创建清洗后数据集合
db.createCollection('cleaned_data', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['source_id', 'raw_data_id', 'data', 'created_at'],
      properties: {
        source_id: {
          bsonType: 'string',
          description: '数据源ID'
        },
        raw_data_id: {
          bsonType: 'objectId',
          description: '原始数据ID'
        },
        task_id: {
          bsonType: 'string',
          description: '任务ID'
        },
        data: {
          bsonType: 'object',
          description: '清洗后的数据内容'
        },
        cleaning_rules: {
          bsonType: 'array',
          description: '应用的清洗规则'
        },
        quality_score: {
          bsonType: 'number',
          minimum: 0,
          maximum: 1,
          description: '数据质量评分'
        },
        created_at: {
          bsonType: 'date',
          description: '创建时间'
        }
      }
    }
  }
});

// 创建数据处理会话集合
db.createCollection('processing_sessions', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['session_id', 'status', 'created_at'],
      properties: {
        session_id: {
          bsonType: 'string',
          description: '会话ID'
        },
        task_ids: {
          bsonType: 'array',
          description: '关联的任务ID列表'
        },
        status: {
          bsonType: 'string',
          enum: ['running', 'completed', 'failed', 'cancelled'],
          description: '会话状态'
        },
        progress: {
          bsonType: 'object',
          description: '处理进度信息'
        },
        error_info: {
          bsonType: 'object',
          description: '错误信息'
        },
        created_at: {
          bsonType: 'date',
          description: '创建时间'
        },
        completed_at: {
          bsonType: 'date',
          description: '完成时间'
        }
      }
    }
  }
});

// 创建索引
// raw_data集合索引
db.raw_data.createIndex({ 'source_id': 1 });
db.raw_data.createIndex({ 'task_id': 1 });
db.raw_data.createIndex({ 'created_at': -1 });
db.raw_data.createIndex({ 'source_id': 1, 'created_at': -1 });

// cleaned_data集合索引
db.cleaned_data.createIndex({ 'source_id': 1 });
db.cleaned_data.createIndex({ 'raw_data_id': 1 });
db.cleaned_data.createIndex({ 'task_id': 1 });
db.cleaned_data.createIndex({ 'created_at': -1 });
db.cleaned_data.createIndex({ 'quality_score': -1 });
db.cleaned_data.createIndex({ 'source_id': 1, 'created_at': -1 });

// processing_sessions集合索引
db.processing_sessions.createIndex({ 'session_id': 1 }, { unique: true });
db.processing_sessions.createIndex({ 'status': 1 });
db.processing_sessions.createIndex({ 'created_at': -1 });

// 插入示例数据
db.raw_data.insertMany([
  {
    source_id: 'example_api_source',
    task_id: 'task_001',
    data: {
      title: '示例文章标题',
      content: '这是一个示例文章的内容...',
      author: '示例作者',
      publish_date: '2024-01-01'
    },
    metadata: {
      url: 'https://example.com/article/1',
      crawl_time: new Date(),
      user_agent: 'DataTrans/1.0'
    },
    created_at: new Date()
  },
  {
    source_id: 'example_web_source',
    task_id: 'task_002',
    data: {
      title: '另一个示例标题',
      content: '另一个示例内容...',
      category: '技术'
    },
    metadata: {
      url: 'https://example.com/article/2',
      crawl_time: new Date(),
      response_time: 250
    },
    created_at: new Date()
  }
]);

print('MongoDB数据库初始化完成');
print('- 创建了用户: data_trans_user');
print('- 创建了集合: raw_data, cleaned_data, processing_sessions');
print('- 创建了相关索引');
print('- 插入了示例数据');
