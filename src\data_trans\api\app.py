"""
FastAPI应用主文件

创建和配置FastAPI应用实例。
"""

import logging
from contextlib import asynccontextmanager
from typing import Any, AsyncGenerator, Dict

from fastapi import FastAPI
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from starlette.exceptions import HTTPException

from ..config.settings import get_settings
from .middleware import (
    http_exception_handler,
    setup_middleware,
    validation_exception_handler,
)
from .routes import api_router

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """应用生命周期管理

    Args:
        app: FastAPI应用实例
    """
    # 启动时执行
    logger.info("DataTrans API 启动中...")

    # 这里可以添加启动时的初始化逻辑
    # 例如：连接数据库、初始化缓存等

    yield

    # 关闭时执行
    logger.info("DataTrans API 关闭中...")

    # 这里可以添加关闭时的清理逻辑
    # 例如：关闭数据库连接、清理资源等


def create_app() -> FastAPI:
    """创建FastAPI应用实例

    Returns:
        配置好的FastAPI应用实例
    """
    settings = get_settings()

    # 创建FastAPI应用
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="分布式数据采集、清洗和存储系统",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        openapi_url="/openapi.json" if settings.debug else None,
        lifespan=lifespan,
    )

    # 设置中间件
    setup_middleware(app)

    # 添加异常处理器
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)

    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"] if settings.debug else ["http://localhost:3000"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 添加Gzip压缩中间件
    app.add_middleware(GZipMiddleware, minimum_size=1000)

    # 注册路由
    app.include_router(api_router)

    # 健康检查端点
    @app.get("/health")  # type: ignore[misc]
    async def health_check() -> Dict[str, Any]:
        """健康检查端点"""
        return {
            "status": "healthy",
            "service": settings.app_name,
            "version": settings.app_version,
            "environment": settings.environment.value,
        }

    # 根路径
    @app.get("/")  # type: ignore[misc]
    async def root() -> Dict[str, Any]:
        """根路径"""
        return {
            "message": f"欢迎使用 {settings.app_name}",
            "version": settings.app_version,
            "docs": "/docs" if settings.debug else "文档在生产环境中不可用",
            "health": "/health",
        }

    logger.info(f"FastAPI应用已创建: {settings.app_name} v{settings.app_version}")
    return app


# 创建应用实例
app = create_app()
