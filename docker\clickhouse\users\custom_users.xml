<?xml version="1.0"?>
<clickhouse>
    <users>
        <!-- 数据传输系统专用用户 -->
        <data_trans_user>
            <password>data_trans_password</password>
            <networks>
                <ip>::/0</ip>
            </networks>
            <profile>default</profile>
            <quota>default</quota>
            <databases>
                <database>data_trans</database>
                <database>system</database>
            </databases>
            <access_management>1</access_management>
        </data_trans_user>

        <!-- 只读用户 -->
        <readonly_user>
            <password>readonly_password</password>
            <networks>
                <ip>::/0</ip>
            </networks>
            <profile>readonly</profile>
            <quota>default</quota>
            <databases>
                <database>data_trans</database>
            </databases>
        </readonly_user>
    </users>

    <profiles>
        <!-- 默认配置文件 -->
        <default>
            <max_memory_usage>10000000000</max_memory_usage>
            <use_uncompressed_cache>0</use_uncompressed_cache>
            <load_balancing>random</load_balancing>
            <max_query_size>268435456</max_query_size>
            <max_ast_elements>50000</max_ast_elements>
            <max_expanded_ast_elements>500000</max_expanded_ast_elements>
            <readonly>0</readonly>
        </default>

        <!-- 只读配置文件 -->
        <readonly>
            <max_memory_usage>5000000000</max_memory_usage>
            <use_uncompressed_cache>0</use_uncompressed_cache>
            <load_balancing>random</load_balancing>
            <max_query_size>268435456</max_query_size>
            <max_ast_elements>50000</max_ast_elements>
            <max_expanded_ast_elements>500000</max_expanded_ast_elements>
            <readonly>1</readonly>
        </readonly>
    </profiles>

    <quotas>
        <!-- 默认配额 -->
        <default>
            <interval>
                <duration>3600</duration>
                <queries>0</queries>
                <errors>0</errors>
                <result_rows>0</result_rows>
                <read_rows>0</read_rows>
                <execution_time>0</execution_time>
            </interval>
        </default>
    </quotas>
</clickhouse>
