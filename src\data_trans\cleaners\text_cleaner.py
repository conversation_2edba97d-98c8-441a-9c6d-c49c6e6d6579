"""
文本数据清洗器

专门用于清洗和处理文本数据的清洗器。
"""

import logging
import re
import time
from typing import Any, Dict, List, Optional, Set

from pydantic import Field

from .base_cleaner import BaseCleaner, CleanerConfig, CleaningResult

logger = logging.getLogger(__name__)


class TextCleanerConfig(CleanerConfig):
    """文本清洗器配置类"""

    # 文本处理配置
    remove_html: bool = Field(default=True, description="移除HTML标签")
    remove_urls: bool = Field(default=False, description="移除URL")
    remove_emails: bool = Field(default=False, description="移除邮箱地址")
    remove_phone_numbers: bool = Field(default=False, description="移除电话号码")
    remove_special_chars: bool = Field(default=False, description="移除特殊字符")
    remove_extra_whitespace: bool = Field(default=True, description="移除多余空白")

    # 编码处理
    normalize_unicode: bool = Field(default=True, description="标准化Unicode字符")
    fix_encoding: bool = Field(default=True, description="修复编码问题")

    # 语言处理
    detect_language: bool = Field(default=False, description="检测语言")
    filter_languages: List[str] = Field(
        default_factory=list, description="过滤特定语言"
    )

    # 内容过滤
    min_length: int = Field(default=0, description="最小文本长度")
    max_length: int = Field(default=0, description="最大文本长度，0表示无限制")
    forbidden_words: List[str] = Field(default_factory=list, description="禁用词列表")

    # 格式化
    lowercase: bool = Field(default=False, description="转换为小写")
    uppercase: bool = Field(default=False, description="转换为大写")
    title_case: bool = Field(default=False, description="转换为标题格式")


class TextCleaner(BaseCleaner):
    """文本数据清洗器"""

    def __init__(self, config: TextCleanerConfig) -> None:
        """初始化文本清洗器

        Args:
            config: 文本清洗器配置
        """
        super().__init__(config)
        self.config: TextCleanerConfig = config

        # 预编译正则表达式
        self._html_pattern = re.compile(r"<[^>]+>")
        self._url_pattern = re.compile(
            r"http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|"
            r"(?:%[0-9a-fA-F][0-9a-fA-F]))+"
        )
        self._email_pattern = re.compile(
            r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
        )
        self._phone_pattern = re.compile(
            r"(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})"
        )
        self._whitespace_pattern = re.compile(r"\s+")
        self._special_chars_pattern = re.compile(r"[^\w\s\u4e00-\u9fff]")

        # 禁用词集合（提高查找效率）
        self._forbidden_words_set: Set[str] = set(
            word.lower() for word in self.config.forbidden_words
        )

    async def clean_single(self, data: Dict[str, Any]) -> CleaningResult:
        """清洗单个数据项

        Args:
            data: 要清洗的数据

        Returns:
            清洗结果
        """
        start_time = time.time()

        # 首先应用基础规则
        base_result = self._apply_rules(data)

        # 然后应用文本特定的清洗
        cleaned_data = base_result.cleaned_data.copy()
        applied_rules = base_result.applied_rules.copy()
        errors = base_result.errors.copy()
        warnings = base_result.warnings.copy()

        # 对所有字符串字段应用文本清洗
        for field, value in cleaned_data.items():
            if isinstance(value, str):
                try:
                    cleaned_value = await self._clean_text(value)
                    cleaned_data[field] = cleaned_value
                    applied_rules.append(f"text_cleaning_{field}")
                except Exception as e:
                    error_msg = f"清洗字段 {field} 失败: {e}"
                    errors.append(error_msg)

                    if self.config.strict_mode:
                        raise RuntimeError(error_msg)

        duration = time.time() - start_time

        return CleaningResult(
            original_data=data,
            cleaned_data=cleaned_data,
            applied_rules=applied_rules,
            errors=errors,
            warnings=warnings,
            duration=duration,
        )

    async def _clean_text(self, text: str) -> str:
        """清洗文本内容

        Args:
            text: 原始文本

        Returns:
            清洗后的文本
        """
        if not text:
            return text

        cleaned_text = text

        # 修复编码问题
        if self.config.fix_encoding:
            cleaned_text = self._fix_encoding(cleaned_text)

        # 标准化Unicode
        if self.config.normalize_unicode:
            cleaned_text = self._normalize_unicode(cleaned_text)

        # 移除HTML标签
        if self.config.remove_html:
            cleaned_text = self._html_pattern.sub("", cleaned_text)

        # 移除URL
        if self.config.remove_urls:
            cleaned_text = self._url_pattern.sub("", cleaned_text)

        # 移除邮箱
        if self.config.remove_emails:
            cleaned_text = self._email_pattern.sub("", cleaned_text)

        # 移除电话号码
        if self.config.remove_phone_numbers:
            cleaned_text = self._phone_pattern.sub("", cleaned_text)

        # 移除特殊字符
        if self.config.remove_special_chars:
            cleaned_text = self._special_chars_pattern.sub(" ", cleaned_text)

        # 移除多余空白
        if self.config.remove_extra_whitespace:
            cleaned_text = self._whitespace_pattern.sub(" ", cleaned_text).strip()

        # 长度过滤
        if self.config.min_length > 0 and len(cleaned_text) < self.config.min_length:
            raise ValueError(
                f"文本长度 {len(cleaned_text)} 小于最小长度 {self.config.min_length}"
            )

        if self.config.max_length > 0 and len(cleaned_text) > self.config.max_length:
            cleaned_text = cleaned_text[: self.config.max_length]

        # 禁用词过滤
        if self._forbidden_words_set:
            cleaned_text = self._filter_forbidden_words(cleaned_text)

        # 格式化
        if self.config.lowercase:
            cleaned_text = cleaned_text.lower()
        elif self.config.uppercase:
            cleaned_text = cleaned_text.upper()
        elif self.config.title_case:
            cleaned_text = cleaned_text.title()

        return cleaned_text

    def _fix_encoding(self, text: str) -> str:
        """修复编码问题

        Args:
            text: 原始文本

        Returns:
            修复后的文本
        """
        try:
            # 尝试修复常见的编码问题
            # 处理Windows-1252编码问题
            text = text.encode("utf-8", errors="ignore").decode("utf-8")

            # 处理HTML实体
            import html

            text = html.unescape(text)

            return text
        except Exception as e:
            logger.warning(f"修复编码失败: {e}")
            return text

    def _normalize_unicode(self, text: str) -> str:
        """标准化Unicode字符

        Args:
            text: 原始文本

        Returns:
            标准化后的文本
        """
        try:
            import unicodedata

            # 使用NFKC标准化
            return unicodedata.normalize("NFKC", text)
        except Exception as e:
            logger.warning(f"Unicode标准化失败: {e}")
            return text

    def _filter_forbidden_words(self, text: str) -> str:
        """过滤禁用词

        Args:
            text: 原始文本

        Returns:
            过滤后的文本
        """
        words = text.split()
        filtered_words = []

        for word in words:
            # 移除标点符号后检查
            clean_word = re.sub(r"[^\w]", "", word.lower())
            if clean_word not in self._forbidden_words_set:
                filtered_words.append(word)
            else:
                filtered_words.append("***")  # 用星号替换禁用词

        return " ".join(filtered_words)

    def detect_language(self, text: str) -> Optional[str]:
        """检测文本语言

        Args:
            text: 文本内容

        Returns:
            语言代码，如果检测失败则返回None
        """
        try:
            # 这里可以集成langdetect或其他语言检测库
            # 为了减少依赖，这里使用简单的启发式方法

            # 检测中文
            chinese_chars = len(re.findall(r"[\u4e00-\u9fff]", text))
            total_chars = len(re.findall(r"[a-zA-Z\u4e00-\u9fff]", text))

            if total_chars == 0:
                return None

            chinese_ratio = chinese_chars / total_chars

            if chinese_ratio > 0.3:
                return "zh"
            else:
                return "en"

        except Exception as e:
            logger.warning(f"语言检测失败: {e}")
            return None

    def extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """提取关键词

        Args:
            text: 文本内容
            max_keywords: 最大关键词数量

        Returns:
            关键词列表
        """
        try:
            # 简单的关键词提取（基于词频）
            # 清理文本
            cleaned_text = re.sub(r"[^\w\s\u4e00-\u9fff]", " ", text.lower())
            words = cleaned_text.split()

            # 过滤停用词（简单版本）
            stop_words = {
                "the",
                "a",
                "an",
                "and",
                "or",
                "but",
                "in",
                "on",
                "at",
                "to",
                "for",
                "of",
                "with",
                "by",
                "is",
                "are",
                "was",
                "were",
                "be",
                "been",
                "have",
                "has",
                "had",
                "do",
                "does",
                "did",
                "will",
                "would",
                "could",
                "should",
                "的",
                "了",
                "在",
                "是",
                "我",
                "你",
                "他",
                "她",
                "它",
                "们",
                "这",
                "那",
                "有",
                "没",
                "不",
                "也",
                "都",
                "很",
                "就",
                "还",
                "要",
                "可以",
                "能够",
            }

            # 统计词频
            word_freq: Dict[str, int] = {}
            for word in words:
                if len(word) > 1 and word not in stop_words:
                    word_freq[word] = word_freq.get(word, 0) + 1

            # 按频率排序并返回前N个
            sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
            return [word for word, freq in sorted_words[:max_keywords]]

        except Exception as e:
            logger.warning(f"关键词提取失败: {e}")
            return []

    def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """提取命名实体

        Args:
            text: 文本内容

        Returns:
            实体字典，键为实体类型，值为实体列表
        """
        entities: Dict[str, List[str]] = {
            "emails": [],
            "urls": [],
            "phone_numbers": [],
            "dates": [],
            "numbers": [],
        }

        try:
            # 提取邮箱
            emails = self._email_pattern.findall(text)
            entities["emails"] = emails

            # 提取URL
            urls = self._url_pattern.findall(text)
            entities["urls"] = urls

            # 提取电话号码
            phones = self._phone_pattern.findall(text)
            entities["phone_numbers"] = ["".join(phone) for phone in phones]

            # 提取日期（简单模式）
            date_pattern = re.compile(
                r"\d{4}[-/]\d{1,2}[-/]\d{1,2}|\d{1,2}[-/]\d{1,2}[-/]\d{4}"
            )
            dates = date_pattern.findall(text)
            entities["dates"] = dates

            # 提取数字
            number_pattern = re.compile(r"\b\d+(?:\.\d+)?\b")
            numbers = number_pattern.findall(text)
            entities["numbers"] = numbers

        except Exception as e:
            logger.warning(f"实体提取失败: {e}")

        return entities

    def calculate_readability(self, text: str) -> Dict[str, float]:
        """计算文本可读性指标

        Args:
            text: 文本内容

        Returns:
            可读性指标字典
        """
        try:
            # 基本统计
            sentences = re.split(r"[.!?]+", text)
            sentences = [s.strip() for s in sentences if s.strip()]

            words = re.findall(r"\b\w+\b", text)

            if not sentences or not words:
                return {}

            # 计算指标
            avg_sentence_length = len(words) / len(sentences)
            avg_word_length = sum(len(word) for word in words) / len(words)

            # 简化的可读性评分（0-100，100最易读）
            readability_score = max(
                0, min(100, 100 - avg_sentence_length - avg_word_length)
            )

            return {
                "sentence_count": len(sentences),
                "word_count": len(words),
                "avg_sentence_length": avg_sentence_length,
                "avg_word_length": avg_word_length,
                "readability_score": readability_score,
            }

        except Exception as e:
            logger.warning(f"可读性计算失败: {e}")
            return {}

    def validate_data(self, data: Dict[str, Any]) -> List[str]:
        """验证文本数据

        Args:
            data: 要验证的数据

        Returns:
            验证错误列表
        """
        errors = super().validate_data(data)

        # 检查是否包含文本字段
        has_text_field = any(isinstance(value, str) for value in data.values())
        if not has_text_field:
            errors.append("数据中没有文本字段")

        # 检查语言过滤
        if self.config.filter_languages:
            for field, value in data.items():
                if isinstance(value, str):
                    detected_lang = self.detect_language(value)
                    if (
                        detected_lang
                        and detected_lang not in self.config.filter_languages
                    ):
                        errors.append(
                            f"字段 {field} 的语言 {detected_lang} 不在允许列表中"
                        )

        return errors
