# Redis 7.0+ 配置文件
# 数据传输系统专用配置

# 网络配置
bind 0.0.0.0
port 6379
protected-mode no

# 通用配置
daemonize no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""

# 数据库配置
databases 16
save 900 1
save 300 10
save 60 10000

# 持久化配置
# RDB持久化
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# AOF持久化
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 内存管理
maxmemory 2gb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# 客户端配置
timeout 300
tcp-keepalive 300
tcp-backlog 511

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 延迟监控
latency-monitor-threshold 100

# 客户端输出缓冲区限制
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# 哈希表配置
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# 列表配置
list-max-ziplist-size -2
list-compress-depth 0

# 集合配置
set-max-intset-entries 512

# 有序集合配置
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog配置
hll-sparse-max-bytes 3000

# 流配置
stream-node-max-bytes 4096
stream-node-max-entries 100

# 活跃重新哈希
activerehashing yes

# 客户端查询缓冲区限制
client-query-buffer-limit 1gb

# 协议最大批量请求大小
proto-max-bulk-len 512mb

# 频率计数器配置
hz 10

# 动态频率调整
dynamic-hz yes

# AOF重写期间允许的最大延迟
aof-rewrite-incremental-fsync yes

# RDB文件加载时的内存使用优化
rdb-save-incremental-fsync yes

# 启用活跃过期
activedefrag yes
active-defrag-ignore-bytes 100mb
active-defrag-threshold-lower 10
active-defrag-threshold-upper 100
active-defrag-cycle-min 1
active-defrag-cycle-max 25

# Lua脚本配置
lua-time-limit 5000

# 通知配置
notify-keyspace-events ""

# 安全配置
# requirepass your_redis_password_here

# 禁用危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
rename-command CONFIG ""
