<?xml version="1.0"?>
<clickhouse>
    <!-- 自定义ClickHouse配置 -->

    <!-- 网络配置 -->
    <listen_host>0.0.0.0</listen_host>

    <!-- 日志配置 -->
    <logger>
        <level>information</level>
        <log>/var/log/clickhouse-server/clickhouse-server.log</log>
        <errorlog>/var/log/clickhouse-server/clickhouse-server.err.log</errorlog>
        <size>1000M</size>
        <count>10</count>
    </logger>

    <!-- 查询配置 -->
    <max_concurrent_queries>100</max_concurrent_queries>
    <max_server_memory_usage>0</max_server_memory_usage>
    <max_thread_pool_size>10000</max_thread_pool_size>

    <!-- 压缩配置 -->
    <compression>
        <case>
            <method>lz4</method>
        </case>
    </compression>

    <!-- 分布式DDL配置 -->
    <distributed_ddl>
        <path>/clickhouse/task_queue/ddl</path>
    </distributed_ddl>

    <!-- 数据类型配置 -->
    <format_schema_path>/var/lib/clickhouse/format_schemas/</format_schema_path>

    <!-- 查询日志配置 -->
    <query_log>
        <database>system</database>
        <table>query_log</table>
        <partition_by>toYYYYMM(event_date)</partition_by>
        <flush_interval_milliseconds>7500</flush_interval_milliseconds>
    </query_log>

    <!-- 查询线程日志配置 -->
    <query_thread_log>
        <database>system</database>
        <table>query_thread_log</table>
        <partition_by>toYYYYMM(event_date)</partition_by>
        <flush_interval_milliseconds>7500</flush_interval_milliseconds>
    </query_thread_log>

    <!-- 部分日志配置 -->
    <part_log>
        <database>system</database>
        <table>part_log</table>
        <partition_by>toYYYYMM(event_date)</partition_by>
        <flush_interval_milliseconds>7500</flush_interval_milliseconds>
    </part_log>

    <!-- 性能优化配置 -->
    <merge_tree>
        <max_suspicious_broken_parts>5</max_suspicious_broken_parts>
    </merge_tree>

    <!-- 字典配置路径 -->
    <dictionaries_config>*_dictionary.xml</dictionaries_config>

    <!-- 用户定义函数配置 -->
    <user_defined_executable_functions_config>*_function.xml</user_defined_executable_functions_config>

    <!-- 时区配置 -->
    <timezone>Asia/Shanghai</timezone>
</clickhouse>
