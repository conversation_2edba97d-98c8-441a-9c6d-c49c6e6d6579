"""
基础存储抽象类

定义了所有存储后端必须实现的接口，确保一致性和可扩展性。
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple

from pydantic import BaseModel


class StorageConfig(BaseModel):
    """存储配置基类"""

    connection_timeout: float = 30.0
    max_retries: int = 3
    retry_delay: float = 1.0


class BaseStorage(ABC):
    """存储后端基类

    所有存储实现都必须继承此类并实现抽象方法。
    """

    def __init__(self, config: StorageConfig) -> None:
        """初始化存储后端

        Args:
            config: 存储配置
        """
        self.config = config
        self._connected = False

    @abstractmethod
    async def connect(self) -> None:
        """建立连接"""
        pass

    @abstractmethod
    async def disconnect(self) -> None:
        """断开连接"""
        pass

    @abstractmethod
    async def ping(self) -> bool:
        """检查连接状态"""
        pass

    @abstractmethod
    async def create_collection(self, name: str, **kwargs: Any) -> bool:
        """创建集合/表

        Args:
            name: 集合/表名称
            **kwargs: 额外参数

        Returns:
            是否创建成功
        """
        pass

    @abstractmethod
    async def drop_collection(self, name: str) -> bool:
        """删除集合/表

        Args:
            name: 集合/表名称

        Returns:
            是否删除成功
        """
        pass

    @abstractmethod
    async def insert_one(self, collection: str, document: Dict[str, Any]) -> str:
        """插入单个文档

        Args:
            collection: 集合/表名称
            document: 要插入的文档

        Returns:
            插入文档的ID
        """
        pass

    @abstractmethod
    async def insert_many(
        self, collection: str, documents: List[Dict[str, Any]]
    ) -> List[str]:
        """批量插入文档

        Args:
            collection: 集合/表名称
            documents: 要插入的文档列表

        Returns:
            插入文档的ID列表
        """
        pass

    @abstractmethod
    async def find_one(
        self, collection: str, filter_dict: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """查找单个文档

        Args:
            collection: 集合/表名称
            filter_dict: 查询条件

        Returns:
            找到的文档，如果没有找到则返回None
        """
        pass

    @abstractmethod
    async def find_many(
        self,
        collection: str,
        filter_dict: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None,
        skip: Optional[int] = None,
        sort: Optional[List[Tuple[str, int]]] = None,
    ) -> List[Dict[str, Any]]:
        """查找多个文档

        Args:
            collection: 集合/表名称
            filter_dict: 查询条件
            limit: 限制返回数量
            skip: 跳过数量
            sort: 排序条件

        Returns:
            找到的文档列表
        """
        pass

    @abstractmethod
    async def update_one(
        self,
        collection: str,
        filter_dict: Dict[str, Any],
        update_dict: Dict[str, Any],
        upsert: bool = False,
    ) -> bool:
        """更新单个文档

        Args:
            collection: 集合/表名称
            filter_dict: 查询条件
            update_dict: 更新内容
            upsert: 如果不存在是否插入

        Returns:
            是否更新成功
        """
        pass

    @abstractmethod
    async def update_many(
        self,
        collection: str,
        filter_dict: Dict[str, Any],
        update_dict: Dict[str, Any],
    ) -> int:
        """批量更新文档

        Args:
            collection: 集合/表名称
            filter_dict: 查询条件
            update_dict: 更新内容

        Returns:
            更新的文档数量
        """
        pass

    @abstractmethod
    async def delete_one(self, collection: str, filter_dict: Dict[str, Any]) -> bool:
        """删除单个文档

        Args:
            collection: 集合/表名称
            filter_dict: 查询条件

        Returns:
            是否删除成功
        """
        pass

    @abstractmethod
    async def delete_many(self, collection: str, filter_dict: Dict[str, Any]) -> int:
        """批量删除文档

        Args:
            collection: 集合/表名称
            filter_dict: 查询条件

        Returns:
            删除的文档数量
        """
        pass

    @abstractmethod
    async def count(
        self, collection: str, filter_dict: Optional[Dict[str, Any]] = None
    ) -> int:
        """统计文档数量

        Args:
            collection: 集合/表名称
            filter_dict: 查询条件

        Returns:
            文档数量
        """
        pass

    @property
    def is_connected(self) -> bool:
        """是否已连接"""
        return self._connected

    async def __aenter__(self) -> "BaseStorage":
        """异步上下文管理器入口"""
        await self.connect()
        return self

    async def __aexit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        """异步上下文管理器出口"""
        await self.disconnect()
