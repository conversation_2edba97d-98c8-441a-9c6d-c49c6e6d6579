#!/bin/bash

# DataTrans 分布式数据传输系统停止脚本
# 用于安全停止所有Docker服务

set -e

echo "🛑 停止 DataTrans 分布式数据传输系统..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ 错误: Docker 未运行"
    exit 1
fi

# 检查docker-compose是否可用
if ! command -v docker-compose &> /dev/null; then
    echo "❌ 错误: docker-compose 未安装"
    exit 1
fi

# 显示当前运行的服务
echo "📋 当前运行的服务:"
docker-compose ps

# 优雅停止服务（按反向依赖顺序）
echo "🔄 停止应用服务..."

echo "📦 停止对象存储服务..."
docker-compose stop minio

echo "🔄 停止消息队列服务..."
docker-compose stop kafka
docker-compose stop zookeeper

echo "🗄️  停止缓存服务..."
docker-compose stop redis

echo "🗄️  停止数据库服务..."
docker-compose stop clickhouse
docker-compose stop mongodb
docker-compose stop postgres

echo "✅ 所有服务已停止"

# 选择性清理
read -p "🗑️  是否要删除容器? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  删除容器..."
    docker-compose down
    echo "✅ 容器已删除"
fi

read -p "🗑️  是否要删除数据卷? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "⚠️  警告: 这将删除所有数据!"
    read -p "确认删除数据卷? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🗑️  删除数据卷..."
        docker-compose down -v
        echo "✅ 数据卷已删除"
    fi
fi

read -p "🗑️  是否要删除网络? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  删除网络..."
    docker-compose down --remove-orphans
    echo "✅ 网络已删除"
fi

echo ""
echo "🎉 DataTrans 系统已停止"
echo ""
echo "📝 重新启动: ./scripts/start.sh"
echo "🔍 查看状态: docker-compose ps"
echo ""
