"""
数据验证器

用于验证数据格式、完整性和一致性的验证器。
"""

import logging
import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Pattern

from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class ValidationRule(BaseModel):
    """数据验证规则"""

    name: str = Field(description="规则名称")
    field: str = Field(description="目标字段")
    rule_type: str = Field(description="规则类型")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="规则参数")
    required: bool = Field(default=True, description="字段是否必需")
    error_message: Optional[str] = Field(default=None, description="自定义错误消息")


class ValidationResult(BaseModel):
    """验证结果"""

    is_valid: bool = Field(description="是否验证通过")
    errors: List[str] = Field(default_factory=list, description="错误信息")
    warnings: List[str] = Field(default_factory=list, description="警告信息")
    field_results: Dict[str, bool] = Field(
        default_factory=dict, description="字段验证结果"
    )
    validated_data: Dict[str, Any] = Field(
        default_factory=dict, description="验证后的数据"
    )


class DataValidator:
    """数据验证器"""

    def __init__(self) -> None:
        """初始化数据验证器"""
        self.rules: List[ValidationRule] = []
        self._compiled_patterns: Dict[str, Pattern[str]] = {}

    def add_rule(self, rule: ValidationRule) -> None:
        """添加验证规则

        Args:
            rule: 验证规则
        """
        self.rules.append(rule)

        # 预编译正则表达式
        if rule.rule_type == "regex" and "pattern" in rule.parameters:
            pattern = rule.parameters["pattern"]
            self._compiled_patterns[rule.name] = re.compile(pattern)

        logger.info(f"添加验证规则: {rule.name}")

    def remove_rule(self, rule_name: str) -> bool:
        """移除验证规则

        Args:
            rule_name: 规则名称

        Returns:
            是否移除成功
        """
        for i, rule in enumerate(self.rules):
            if rule.name == rule_name:
                del self.rules[i]
                if rule_name in self._compiled_patterns:
                    del self._compiled_patterns[rule_name]
                logger.info(f"移除验证规则: {rule_name}")
                return True
        return False

    def validate(self, data: Dict[str, Any]) -> ValidationResult:
        """验证数据

        Args:
            data: 要验证的数据

        Returns:
            验证结果
        """
        result = ValidationResult(is_valid=True, validated_data=data.copy())

        # 应用所有验证规则
        for rule in self.rules:
            field_result = self._validate_field(data, rule)

            result.field_results[rule.field] = field_result.is_valid
            result.errors.extend(field_result.errors)
            result.warnings.extend(field_result.warnings)

            if not field_result.is_valid:
                result.is_valid = False

        return result

    def _validate_field(
        self, data: Dict[str, Any], rule: ValidationRule
    ) -> ValidationResult:
        """验证单个字段

        Args:
            data: 数据
            rule: 验证规则

        Returns:
            字段验证结果
        """
        field_result = ValidationResult(is_valid=True)
        field_value = data.get(rule.field)

        # 检查必需字段
        if rule.required and (field_value is None or field_value == ""):
            error_msg = rule.error_message or f"字段 {rule.field} 是必需的"
            field_result.errors.append(error_msg)
            field_result.is_valid = False
            return field_result

        # 如果字段不存在且不是必需的，跳过验证
        if field_value is None:
            return field_result

        # 根据规则类型进行验证
        try:
            if rule.rule_type == "type":
                self._validate_type(field_value, rule, field_result)

            elif rule.rule_type == "range":
                self._validate_range(field_value, rule, field_result)

            elif rule.rule_type == "length":
                self._validate_length(field_value, rule, field_result)

            elif rule.rule_type == "regex":
                self._validate_regex(field_value, rule, field_result)

            elif rule.rule_type == "email":
                self._validate_email(field_value, rule, field_result)

            elif rule.rule_type == "url":
                self._validate_url(field_value, rule, field_result)

            elif rule.rule_type == "date":
                self._validate_date(field_value, rule, field_result)

            elif rule.rule_type == "enum":
                self._validate_enum(field_value, rule, field_result)

            elif rule.rule_type == "custom":
                self._validate_custom(field_value, rule, field_result)

            else:
                field_result.warnings.append(f"未知的验证规则类型: {rule.rule_type}")

        except Exception as e:
            error_msg = rule.error_message or f"验证字段 {rule.field} 时出错: {e}"
            field_result.errors.append(error_msg)
            field_result.is_valid = False

        return field_result

    def _validate_type(
        self, value: Any, rule: ValidationRule, result: ValidationResult
    ) -> None:
        """验证数据类型

        Args:
            value: 字段值
            rule: 验证规则
            result: 验证结果
        """
        expected_type = rule.parameters.get("type")

        type_mapping = {
            "str": str,
            "int": int,
            "float": float,
            "bool": bool,
            "list": list,
            "dict": dict,
        }

        if expected_type in type_mapping:
            expected_class = type_mapping[expected_type]
            if not isinstance(value, expected_class):
                error_msg = (
                    rule.error_message
                    or f"字段 {rule.field} 应该是 {expected_type} 类型"
                )
                result.errors.append(error_msg)
                result.is_valid = False
        else:
            result.warnings.append(f"未知的类型: {expected_type}")

    def _validate_range(
        self, value: Any, rule: ValidationRule, result: ValidationResult
    ) -> None:
        """验证数值范围

        Args:
            value: 字段值
            rule: 验证规则
            result: 验证结果
        """
        if not isinstance(value, (int, float)):
            error_msg = rule.error_message or f"字段 {rule.field} 必须是数值类型"
            result.errors.append(error_msg)
            result.is_valid = False
            return

        min_value = rule.parameters.get("min")
        max_value = rule.parameters.get("max")

        if min_value is not None and value < min_value:
            error_msg = rule.error_message or f"字段 {rule.field} 不能小于 {min_value}"
            result.errors.append(error_msg)
            result.is_valid = False

        if max_value is not None and value > max_value:
            error_msg = rule.error_message or f"字段 {rule.field} 不能大于 {max_value}"
            result.errors.append(error_msg)
            result.is_valid = False

    def _validate_length(
        self, value: Any, rule: ValidationRule, result: ValidationResult
    ) -> None:
        """验证长度

        Args:
            value: 字段值
            rule: 验证规则
            result: 验证结果
        """
        if not hasattr(value, "__len__"):
            error_msg = rule.error_message or f"字段 {rule.field} 没有长度属性"
            result.errors.append(error_msg)
            result.is_valid = False
            return

        length = len(value)
        min_length = rule.parameters.get("min")
        max_length = rule.parameters.get("max")

        if min_length is not None and length < min_length:
            error_msg = (
                rule.error_message or f"字段 {rule.field} 长度不能小于 {min_length}"
            )
            result.errors.append(error_msg)
            result.is_valid = False

        if max_length is not None and length > max_length:
            error_msg = (
                rule.error_message or f"字段 {rule.field} 长度不能大于 {max_length}"
            )
            result.errors.append(error_msg)
            result.is_valid = False

    def _validate_regex(
        self, value: Any, rule: ValidationRule, result: ValidationResult
    ) -> None:
        """验证正则表达式

        Args:
            value: 字段值
            rule: 验证规则
            result: 验证结果
        """
        if not isinstance(value, str):
            error_msg = rule.error_message or f"字段 {rule.field} 必须是字符串类型"
            result.errors.append(error_msg)
            result.is_valid = False
            return

        pattern = self._compiled_patterns.get(rule.name)
        if not pattern:
            pattern_str = rule.parameters.get("pattern")
            if not pattern_str:
                result.warnings.append(f"正则表达式规则 {rule.name} 缺少pattern参数")
                return
            pattern = re.compile(pattern_str)
            self._compiled_patterns[rule.name] = pattern

        if not pattern.match(value):
            error_msg = rule.error_message or f"字段 {rule.field} 格式不正确"
            result.errors.append(error_msg)
            result.is_valid = False

    def _validate_email(
        self, value: Any, rule: ValidationRule, result: ValidationResult
    ) -> None:
        """验证邮箱格式

        Args:
            value: 字段值
            rule: 验证规则
            result: 验证结果
        """
        if not isinstance(value, str):
            error_msg = rule.error_message or f"字段 {rule.field} 必须是字符串类型"
            result.errors.append(error_msg)
            result.is_valid = False
            return

        email_pattern = re.compile(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
        if not email_pattern.match(value):
            error_msg = rule.error_message or f"字段 {rule.field} 不是有效的邮箱地址"
            result.errors.append(error_msg)
            result.is_valid = False

    def _validate_url(
        self, value: Any, rule: ValidationRule, result: ValidationResult
    ) -> None:
        """验证URL格式

        Args:
            value: 字段值
            rule: 验证规则
            result: 验证结果
        """
        if not isinstance(value, str):
            error_msg = rule.error_message or f"字段 {rule.field} 必须是字符串类型"
            result.errors.append(error_msg)
            result.is_valid = False
            return

        from urllib.parse import urlparse

        try:
            parsed = urlparse(value)
            if not all([parsed.scheme, parsed.netloc]):
                raise ValueError("URL格式不完整")
        except Exception:
            error_msg = rule.error_message or f"字段 {rule.field} 不是有效的URL"
            result.errors.append(error_msg)
            result.is_valid = False

    def _validate_date(
        self, value: Any, rule: ValidationRule, result: ValidationResult
    ) -> None:
        """验证日期格式

        Args:
            value: 字段值
            rule: 验证规则
            result: 验证结果
        """
        if isinstance(value, datetime):
            return  # 已经是datetime对象

        if not isinstance(value, str):
            error_msg = (
                rule.error_message or f"字段 {rule.field} 必须是字符串或datetime类型"
            )
            result.errors.append(error_msg)
            result.is_valid = False
            return

        date_format = rule.parameters.get("format", "%Y-%m-%d")
        try:
            datetime.strptime(value, date_format)
        except ValueError:
            error_msg = (
                rule.error_message
                or f"字段 {rule.field} 日期格式不正确，应为 {date_format}"
            )
            result.errors.append(error_msg)
            result.is_valid = False

    def _validate_enum(
        self, value: Any, rule: ValidationRule, result: ValidationResult
    ) -> None:
        """验证枚举值

        Args:
            value: 字段值
            rule: 验证规则
            result: 验证结果
        """
        allowed_values = rule.parameters.get("values", [])
        if value not in allowed_values:
            error_msg = (
                rule.error_message
                or f"字段 {rule.field} 的值必须是 {allowed_values} 中的一个"
            )
            result.errors.append(error_msg)
            result.is_valid = False

    def _validate_custom(
        self, value: Any, rule: ValidationRule, result: ValidationResult
    ) -> None:
        """自定义验证

        Args:
            value: 字段值
            rule: 验证规则
            result: 验证结果
        """
        validator_func = rule.parameters.get("function")
        if not callable(validator_func):
            result.warnings.append(f"自定义验证规则 {rule.name} 缺少有效的验证函数")
            return

        try:
            is_valid = validator_func(value)
            if not is_valid:
                error_msg = rule.error_message or f"字段 {rule.field} 自定义验证失败"
                result.errors.append(error_msg)
                result.is_valid = False
        except Exception as e:
            error_msg = rule.error_message or f"字段 {rule.field} 自定义验证出错: {e}"
            result.errors.append(error_msg)
            result.is_valid = False

    def validate_schema(
        self, data: Dict[str, Any], schema: Dict[str, Any]
    ) -> ValidationResult:
        """根据模式验证数据

        Args:
            data: 要验证的数据
            schema: 数据模式定义

        Returns:
            验证结果
        """
        result = ValidationResult(is_valid=True, validated_data=data.copy())

        # 验证必需字段
        required_fields = schema.get("required", [])
        for field in required_fields:
            if field not in data or data[field] is None:
                result.errors.append(f"缺少必需字段: {field}")
                result.is_valid = False

        # 验证字段类型和约束
        properties = schema.get("properties", {})
        for field, field_schema in properties.items():
            if field in data:
                field_result = self._validate_field_schema(
                    data[field], field_schema, field
                )
                result.errors.extend(field_result.errors)
                result.warnings.extend(field_result.warnings)
                if not field_result.is_valid:
                    result.is_valid = False

        return result

    def _validate_field_schema(
        self, value: Any, field_schema: Dict[str, Any], field_name: str
    ) -> ValidationResult:
        """根据字段模式验证单个字段

        Args:
            value: 字段值
            field_schema: 字段模式
            field_name: 字段名称

        Returns:
            验证结果
        """
        result = ValidationResult(is_valid=True)

        # 验证类型
        expected_type = field_schema.get("type")
        if expected_type:
            type_mapping = {
                "string": str,
                "integer": int,
                "number": (int, float),
                "boolean": bool,
                "array": list,
                "object": dict,
            }

            if expected_type in type_mapping:
                expected_class = type_mapping[expected_type]
                if not isinstance(value, expected_class):  # type: ignore[arg-type]
                    result.errors.append(
                        f"字段 {field_name} 应该是 {expected_type} 类型"
                    )
                    result.is_valid = False
                    return result

        # 验证字符串约束
        if isinstance(value, str):
            min_length = field_schema.get("minLength")
            max_length = field_schema.get("maxLength")
            pattern = field_schema.get("pattern")

            if min_length is not None and len(value) < min_length:
                result.errors.append(f"字段 {field_name} 长度不能小于 {min_length}")
                result.is_valid = False

            if max_length is not None and len(value) > max_length:
                result.errors.append(f"字段 {field_name} 长度不能大于 {max_length}")
                result.is_valid = False

            if pattern and not re.match(pattern, value):
                result.errors.append(f"字段 {field_name} 格式不匹配模式 {pattern}")
                result.is_valid = False

        # 验证数值约束
        if isinstance(value, (int, float)):
            minimum = field_schema.get("minimum")
            maximum = field_schema.get("maximum")

            if minimum is not None and value < minimum:
                result.errors.append(f"字段 {field_name} 不能小于 {minimum}")
                result.is_valid = False

            if maximum is not None and value > maximum:
                result.errors.append(f"字段 {field_name} 不能大于 {maximum}")
                result.is_valid = False

        # 验证枚举值
        enum_values = field_schema.get("enum")
        if enum_values and value not in enum_values:
            result.errors.append(f"字段 {field_name} 的值必须是 {enum_values} 中的一个")
            result.is_valid = False

        return result

    def get_rules_summary(self) -> Dict[str, Any]:
        """获取规则摘要

        Returns:
            规则摘要信息
        """
        summary = {
            "total_rules": len(self.rules),
            "rules_by_type": {},
            "required_fields": [],
            "optional_fields": [],
        }

        for rule in self.rules:
            # 统计规则类型
            rule_type = rule.rule_type
            rules_by_type = summary["rules_by_type"]
            if isinstance(rules_by_type, dict):
                if rule_type not in rules_by_type:
                    rules_by_type[rule_type] = 0
                rules_by_type[rule_type] += 1

            # 分类字段
            if rule.required:
                if isinstance(summary["required_fields"], list):
                    summary["required_fields"].append(rule.field)
            else:
                if isinstance(summary["optional_fields"], list):
                    summary["optional_fields"].append(rule.field)

        # 去重
        if isinstance(summary["required_fields"], list):
            summary["required_fields"] = list(set(summary["required_fields"]))
        if isinstance(summary["optional_fields"], list):
            summary["optional_fields"] = list(set(summary["optional_fields"]))

        return summary
