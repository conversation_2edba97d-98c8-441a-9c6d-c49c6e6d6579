"""
基础数据清洗抽象类

定义了所有数据清洗器必须实现的接口，确保一致性和可扩展性。
"""

import logging
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class CleaningRule(BaseModel):
    """数据清洗规则"""

    name: str = Field(description="规则名称")
    field: str = Field(description="目标字段")
    operation: str = Field(description="操作类型")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="操作参数")
    enabled: bool = Field(default=True, description="是否启用")
    priority: int = Field(default=0, description="优先级，数字越大优先级越高")


class CleaningResult(BaseModel):
    """数据清洗结果"""

    original_data: Dict[str, Any] = Field(description="原始数据")
    cleaned_data: Dict[str, Any] = Field(description="清洗后数据")
    applied_rules: List[str] = Field(default_factory=list, description="应用的规则")
    errors: List[str] = Field(default_factory=list, description="错误信息")
    warnings: List[str] = Field(default_factory=list, description="警告信息")
    timestamp: datetime = Field(default_factory=datetime.now, description="清洗时间")
    duration: float = Field(description="清洗耗时（秒）")


class CleanerConfig(BaseModel):
    """清洗器配置基类"""

    # 基础配置
    strict_mode: bool = Field(default=False, description="严格模式，遇到错误时停止")
    skip_invalid: bool = Field(default=True, description="跳过无效数据")
    preserve_original: bool = Field(default=True, description="保留原始数据")

    # 验证配置
    validate_input: bool = Field(default=True, description="验证输入数据")
    validate_output: bool = Field(default=True, description="验证输出数据")

    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_errors: bool = Field(default=True, description="记录错误日志")
    log_warnings: bool = Field(default=True, description="记录警告日志")


class BaseCleaner(ABC):
    """数据清洗器基类

    所有数据清洗器都必须继承此类并实现抽象方法。
    """

    def __init__(self, config: CleanerConfig) -> None:
        """初始化清洗器

        Args:
            config: 清洗器配置
        """
        self.config = config
        self.rules: List[CleaningRule] = []
        self._stats = {
            "total_processed": 0,
            "successful_cleaned": 0,
            "failed_cleaned": 0,
            "total_duration": 0.0,
        }

    @abstractmethod
    async def clean_single(self, data: Dict[str, Any]) -> CleaningResult:
        """清洗单个数据项

        Args:
            data: 要清洗的数据

        Returns:
            清洗结果
        """
        pass

    async def clean_multiple(
        self, data_list: List[Dict[str, Any]]
    ) -> List[CleaningResult]:
        """批量清洗数据

        Args:
            data_list: 要清洗的数据列表

        Returns:
            清洗结果列表
        """
        results = []

        for data in data_list:
            try:
                result = await self.clean_single(data)
                results.append(result)

                # 更新统计信息
                self._update_stats(result)

            except Exception as e:
                if self.config.strict_mode:
                    raise

                # 创建错误结果
                error_result = CleaningResult(
                    original_data=data, cleaned_data={}, errors=[str(e)], duration=0.0
                )
                results.append(error_result)

                if self.config.log_errors:
                    logger.error(f"清洗数据失败: {e}")

        return results

    def add_rule(self, rule: CleaningRule) -> None:
        """添加清洗规则

        Args:
            rule: 清洗规则
        """
        self.rules.append(rule)
        # 按优先级排序
        self.rules.sort(key=lambda r: r.priority, reverse=True)
        logger.info(f"添加清洗规则: {rule.name}")

    def remove_rule(self, rule_name: str) -> bool:
        """移除清洗规则

        Args:
            rule_name: 规则名称

        Returns:
            是否移除成功
        """
        for i, rule in enumerate(self.rules):
            if rule.name == rule_name:
                del self.rules[i]
                logger.info(f"移除清洗规则: {rule_name}")
                return True
        return False

    def get_rule(self, rule_name: str) -> Optional[CleaningRule]:
        """获取清洗规则

        Args:
            rule_name: 规则名称

        Returns:
            清洗规则，如果不存在则返回None
        """
        for rule in self.rules:
            if rule.name == rule_name:
                return rule
        return None

    def enable_rule(self, rule_name: str) -> bool:
        """启用清洗规则

        Args:
            rule_name: 规则名称

        Returns:
            是否操作成功
        """
        rule = self.get_rule(rule_name)
        if rule:
            rule.enabled = True
            return True
        return False

    def disable_rule(self, rule_name: str) -> bool:
        """禁用清洗规则

        Args:
            rule_name: 规则名称

        Returns:
            是否操作成功
        """
        rule = self.get_rule(rule_name)
        if rule:
            rule.enabled = False
            return True
        return False

    def _apply_rules(self, data: Dict[str, Any]) -> CleaningResult:
        """应用清洗规则

        Args:
            data: 要清洗的数据

        Returns:
            清洗结果
        """
        import time

        start_time = time.time()
        cleaned_data = data.copy() if self.config.preserve_original else data
        applied_rules = []
        errors = []
        warnings = []

        # 按优先级应用规则
        for rule in self.rules:
            if not rule.enabled:
                continue

            try:
                # 检查字段是否存在
                if rule.field not in cleaned_data:
                    if self.config.skip_invalid:
                        warnings.append(
                            f"字段 {rule.field} 不存在，跳过规则 {rule.name}"
                        )
                        continue
                    else:
                        errors.append(f"字段 {rule.field} 不存在")
                        continue

                # 应用规则
                cleaned_data = self._apply_single_rule(rule, cleaned_data)
                applied_rules.append(rule.name)

            except Exception as e:
                error_msg = f"应用规则 {rule.name} 失败: {e}"
                errors.append(error_msg)

                if self.config.strict_mode:
                    raise RuntimeError(error_msg)

                if self.config.log_errors:
                    logger.error(error_msg)

        duration = time.time() - start_time

        return CleaningResult(
            original_data=data,
            cleaned_data=cleaned_data,
            applied_rules=applied_rules,
            errors=errors,
            warnings=warnings,
            duration=duration,
        )

    def _apply_single_rule(
        self, rule: CleaningRule, data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """应用单个清洗规则

        Args:
            rule: 清洗规则
            data: 数据

        Returns:
            处理后的数据
        """
        field_value = data.get(rule.field)

        # 根据操作类型处理数据
        if rule.operation == "trim":
            # 去除首尾空白
            if isinstance(field_value, str):
                data[rule.field] = field_value.strip()

        elif rule.operation == "lowercase":
            # 转换为小写
            if isinstance(field_value, str):
                data[rule.field] = field_value.lower()

        elif rule.operation == "uppercase":
            # 转换为大写
            if isinstance(field_value, str):
                data[rule.field] = field_value.upper()

        elif rule.operation == "remove_html":
            # 移除HTML标签
            if isinstance(field_value, str):
                import re

                data[rule.field] = re.sub(r"<[^>]+>", "", field_value)

        elif rule.operation == "replace":
            # 替换文本
            if isinstance(field_value, str):
                pattern = rule.parameters.get("pattern", "")
                replacement = rule.parameters.get("replacement", "")
                data[rule.field] = field_value.replace(pattern, replacement)

        elif rule.operation == "regex_replace":
            # 正则表达式替换
            if isinstance(field_value, str):
                import re

                pattern = rule.parameters.get("pattern", "")
                replacement = rule.parameters.get("replacement", "")
                flags = rule.parameters.get("flags", 0)
                data[rule.field] = re.sub(
                    pattern, replacement, field_value, flags=flags
                )

        elif rule.operation == "validate_email":
            # 验证邮箱格式
            if isinstance(field_value, str):
                import re

                email_pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
                if not re.match(email_pattern, field_value):
                    raise ValueError(f"无效的邮箱格式: {field_value}")

        elif rule.operation == "validate_url":
            # 验证URL格式
            if isinstance(field_value, str):
                from urllib.parse import urlparse

                try:
                    result = urlparse(field_value)
                    if not all([result.scheme, result.netloc]):
                        raise ValueError(f"无效的URL格式: {field_value}")
                except Exception:
                    raise ValueError(f"无效的URL格式: {field_value}")

        elif rule.operation == "convert_type":
            # 类型转换
            target_type = rule.parameters.get("type", "str")
            try:
                if target_type == "int":
                    data[rule.field] = int(field_value)  # type: ignore[arg-type]
                elif target_type == "float":
                    data[rule.field] = float(field_value)  # type: ignore[arg-type]
                elif target_type == "str":
                    data[rule.field] = str(field_value)
                elif target_type == "bool":
                    data[rule.field] = bool(field_value)
            except (ValueError, TypeError) as e:
                raise ValueError(f"类型转换失败: {e}")

        elif rule.operation == "set_default":
            # 设置默认值
            if field_value is None or field_value == "":
                default_value = rule.parameters.get("default")
                data[rule.field] = default_value

        elif rule.operation == "remove_field":
            # 移除字段
            if rule.field in data:
                del data[rule.field]

        elif rule.operation == "rename_field":
            # 重命名字段
            new_name = rule.parameters.get("new_name")
            if new_name and rule.field in data:
                data[new_name] = data.pop(rule.field)

        else:
            raise ValueError(f"不支持的操作类型: {rule.operation}")

        return data

    def _update_stats(self, result: CleaningResult) -> None:
        """更新统计信息

        Args:
            result: 清洗结果
        """
        self._stats["total_processed"] += 1
        self._stats["total_duration"] += result.duration

        if result.errors:
            self._stats["failed_cleaned"] += 1
        else:
            self._stats["successful_cleaned"] += 1

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息

        Returns:
            统计信息字典
        """
        stats = self._stats.copy()

        if stats["total_processed"] > 0:
            stats["success_rate"] = (
                stats["successful_cleaned"] / stats["total_processed"]
            )
            stats["failure_rate"] = stats["failed_cleaned"] / stats["total_processed"]
            stats["average_duration"] = (
                stats["total_duration"] / stats["total_processed"]
            )
        else:
            stats["success_rate"] = 0.0
            stats["failure_rate"] = 0.0
            stats["average_duration"] = 0.0

        return stats

    def reset_stats(self) -> None:
        """重置统计信息"""
        self._stats = {
            "total_processed": 0,
            "successful_cleaned": 0,
            "failed_cleaned": 0,
            "total_duration": 0.0,
        }

    def validate_data(self, data: Dict[str, Any]) -> List[str]:
        """验证数据格式

        Args:
            data: 要验证的数据

        Returns:
            验证错误列表
        """
        errors = []

        if not isinstance(data, dict):
            errors.append("数据必须是字典类型")
            return errors

        # 可以在子类中重写此方法添加特定的验证逻辑
        return errors

    def __repr__(self) -> str:
        """字符串表示"""
        return f"{self.__class__.__name__}(rules={len(self.rules)})"
