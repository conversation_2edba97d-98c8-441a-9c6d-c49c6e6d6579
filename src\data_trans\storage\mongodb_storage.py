"""
MongoDB存储后端实现

基于motor异步MongoDB驱动的存储实现。
"""

import logging
from typing import Any, Dict, List, Optional, Tuple, cast

from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pydantic import Field
from pymongo.errors import DuplicateKeyError

from .base_storage import BaseStorage, StorageConfig

logger = logging.getLogger(__name__)


class MongoDBConfig(StorageConfig):
    """MongoDB配置类"""

    host: str = Field(default="localhost")
    port: int = Field(default=27017)
    username: Optional[str] = Field(default=None)
    password: Optional[str] = Field(default=None)
    database: str = Field(default="data_trans")
    auth_source: str = Field(default="admin")
    max_pool_size: int = Field(default=100)
    min_pool_size: int = Field(default=0)
    server_selection_timeout_ms: int = Field(default=30000)


class MongoDBStorage(BaseStorage):
    """MongoDB存储实现"""

    def __init__(self, config: MongoDBConfig) -> None:
        """初始化MongoDB存储

        Args:
            config: MongoDB配置
        """
        super().__init__(config)
        self.config: MongoDBConfig = config
        self._client: Optional[AsyncIOMotorClient[Any]] = None
        self._database: Optional[AsyncIOMotorDatabase[Any]] = None

    async def connect(self) -> None:
        """建立MongoDB连接"""
        try:
            # 构建连接URL
            if self.config.username and self.config.password:
                url = (
                    f"mongodb://{self.config.username}:{self.config.password}"
                    f"@{self.config.host}:{self.config.port}/"
                    f"{self.config.database}?authSource={self.config.auth_source}"
                )
            else:
                url = (
                    f"mongodb://{self.config.host}:{self.config.port}/"
                    f"{self.config.database}"
                )

            # 创建客户端
            self._client = AsyncIOMotorClient(
                url,
                maxPoolSize=self.config.max_pool_size,
                minPoolSize=self.config.min_pool_size,
                serverSelectionTimeoutMS=self.config.server_selection_timeout_ms,
            )

            # 获取数据库
            self._database = self._client[self.config.database]

            # 测试连接
            await self._client.admin.command("ping")
            self._connected = True

            logger.info(f"已连接到MongoDB: {self.config.host}:{self.config.port}")

        except Exception as e:
            logger.error(f"连接MongoDB失败: {e}")
            raise

    async def disconnect(self) -> None:
        """断开MongoDB连接"""
        if self._client:
            self._client.close()
            self._connected = False
            logger.info("已断开MongoDB连接")

    async def ping(self) -> bool:
        """检查MongoDB连接状态"""
        try:
            if not self._client:
                return False
            await self._client.admin.command("ping")
            return True
        except Exception:
            return False

    async def create_collection(self, name: str, **kwargs: Any) -> bool:
        """创建MongoDB集合

        Args:
            name: 集合名称
            **kwargs: 额外参数（如索引定义等）

        Returns:
            是否创建成功
        """
        try:
            if not self._database:
                raise RuntimeError("数据库未连接")

            # 创建集合
            await self._database.create_collection(name, **kwargs)

            logger.info(f"已创建MongoDB集合: {name}")
            return True

        except Exception as e:
            logger.error(f"创建MongoDB集合失败: {e}")
            return False

    async def drop_collection(self, name: str) -> bool:
        """删除MongoDB集合

        Args:
            name: 集合名称

        Returns:
            是否删除成功
        """
        try:
            if not self._database:
                raise RuntimeError("数据库未连接")

            await self._database.drop_collection(name)

            logger.info(f"已删除MongoDB集合: {name}")
            return True

        except Exception as e:
            logger.error(f"删除MongoDB集合失败: {e}")
            return False

    async def insert_one(self, collection: str, document: Dict[str, Any]) -> str:
        """插入单个文档到MongoDB

        Args:
            collection: 集合名称
            document: 要插入的文档

        Returns:
            插入文档的ID
        """
        try:
            if not self._database:
                raise RuntimeError("数据库未连接")

            result = await self._database[collection].insert_one(document)
            return str(result.inserted_id)

        except DuplicateKeyError as e:
            logger.warning(f"文档已存在: {e}")
            raise
        except Exception as e:
            logger.error(f"插入文档失败: {e}")
            raise

    async def insert_many(
        self, collection: str, documents: List[Dict[str, Any]]
    ) -> List[str]:
        """批量插入文档到MongoDB

        Args:
            collection: 集合名称
            documents: 要插入的文档列表

        Returns:
            插入文档的ID列表
        """
        try:
            if not self._database:
                raise RuntimeError("数据库未连接")

            if not documents:
                return []

            result = await self._database[collection].insert_many(documents)
            return [str(oid) for oid in result.inserted_ids]

        except Exception as e:
            logger.error(f"批量插入文档失败: {e}")
            raise

    async def find_one(
        self, collection: str, filter_dict: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """从MongoDB查找单个文档

        Args:
            collection: 集合名称
            filter_dict: 查询条件

        Returns:
            找到的文档，如果没有找到则返回None
        """
        try:
            if not self._database:
                raise RuntimeError("数据库未连接")

            filter_dict = filter_dict or {}
            document = await self._database[collection].find_one(filter_dict)

            if document and "_id" in document:
                document["_id"] = str(document["_id"])

            return cast(Optional[Dict[str, Any]], document)

        except Exception as e:
            logger.error(f"查找文档失败: {e}")
            raise

    async def find_many(
        self,
        collection: str,
        filter_dict: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None,
        skip: Optional[int] = None,
        sort: Optional[List[Tuple[str, int]]] = None,
    ) -> List[Dict[str, Any]]:
        """从MongoDB查找多个文档

        Args:
            collection: 集合名称
            filter_dict: 查询条件
            limit: 限制返回数量
            skip: 跳过数量
            sort: 排序条件

        Returns:
            找到的文档列表
        """
        try:
            if not self._database:
                raise RuntimeError("数据库未连接")

            filter_dict = filter_dict or {}
            cursor = self._database[collection].find(filter_dict)

            if skip:
                cursor = cursor.skip(skip)
            if limit:
                cursor = cursor.limit(limit)
            if sort:
                cursor = cursor.sort(sort)

            documents = await cursor.to_list(length=None)

            # 转换ObjectId为字符串
            for doc in documents:
                if "_id" in doc:
                    doc["_id"] = str(doc["_id"])

            return cast(List[Dict[str, Any]], documents)

        except Exception as e:
            logger.error(f"查找文档失败: {e}")
            raise

    async def update_one(
        self,
        collection: str,
        filter_dict: Dict[str, Any],
        update_dict: Dict[str, Any],
        upsert: bool = False,
    ) -> bool:
        """更新MongoDB中的单个文档

        Args:
            collection: 集合名称
            filter_dict: 查询条件
            update_dict: 更新内容
            upsert: 如果不存在是否插入

        Returns:
            是否更新成功
        """
        try:
            if not self._database:
                raise RuntimeError("数据库未连接")

            # 确保更新操作符格式正确
            if not any(key.startswith("$") for key in update_dict.keys()):
                update_dict = {"$set": update_dict}

            result = await self._database[collection].update_one(
                filter_dict, update_dict, upsert=upsert
            )

            return result.modified_count > 0 or (
                upsert and result.upserted_id is not None
            )

        except Exception as e:
            logger.error(f"更新文档失败: {e}")
            raise

    async def update_many(
        self,
        collection: str,
        filter_dict: Dict[str, Any],
        update_dict: Dict[str, Any],
    ) -> int:
        """批量更新MongoDB中的文档

        Args:
            collection: 集合名称
            filter_dict: 查询条件
            update_dict: 更新内容

        Returns:
            更新的文档数量
        """
        try:
            if not self._database:
                raise RuntimeError("数据库未连接")

            # 确保更新操作符格式正确
            if not any(key.startswith("$") for key in update_dict.keys()):
                update_dict = {"$set": update_dict}

            result = await self._database[collection].update_many(
                filter_dict, update_dict
            )
            return result.modified_count

        except Exception as e:
            logger.error(f"批量更新文档失败: {e}")
            raise

    async def delete_one(self, collection: str, filter_dict: Dict[str, Any]) -> bool:
        """从MongoDB删除单个文档

        Args:
            collection: 集合名称
            filter_dict: 查询条件

        Returns:
            是否删除成功
        """
        try:
            if not self._database:
                raise RuntimeError("数据库未连接")

            result = await self._database[collection].delete_one(filter_dict)
            return result.deleted_count > 0

        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            raise

    async def delete_many(self, collection: str, filter_dict: Dict[str, Any]) -> int:
        """从MongoDB批量删除文档

        Args:
            collection: 集合名称
            filter_dict: 查询条件

        Returns:
            删除的文档数量
        """
        try:
            if not self._database:
                raise RuntimeError("数据库未连接")

            result = await self._database[collection].delete_many(filter_dict)
            return result.deleted_count

        except Exception as e:
            logger.error(f"批量删除文档失败: {e}")
            raise

    async def count(
        self, collection: str, filter_dict: Optional[Dict[str, Any]] = None
    ) -> int:
        """统计MongoDB中的文档数量

        Args:
            collection: 集合名称
            filter_dict: 查询条件

        Returns:
            文档数量
        """
        try:
            if not self._database:
                raise RuntimeError("数据库未连接")

            filter_dict = filter_dict or {}
            count = await self._database[collection].count_documents(filter_dict)
            return count

        except Exception as e:
            logger.error(f"统计文档数量失败: {e}")
            raise

    async def create_index(
        self, collection: str, keys: List[Tuple[str, int]], **kwargs: Any
    ) -> str:
        """创建索引

        Args:
            collection: 集合名称
            keys: 索引键列表，格式为[(field, direction), ...]
            **kwargs: 额外参数

        Returns:
            索引名称
        """
        try:
            if not self._database:
                raise RuntimeError("数据库未连接")

            index_name = await self._database[collection].create_index(keys, **kwargs)
            logger.info(f"已创建索引: {index_name}")
            return index_name

        except Exception as e:
            logger.error(f"创建索引失败: {e}")
            raise

    async def drop_index(self, collection: str, index_name: str) -> bool:
        """删除索引

        Args:
            collection: 集合名称
            index_name: 索引名称

        Returns:
            是否删除成功
        """
        try:
            if not self._database:
                raise RuntimeError("数据库未连接")

            await self._database[collection].drop_index(index_name)
            logger.info(f"已删除索引: {index_name}")
            return True

        except Exception as e:
            logger.error(f"删除索引失败: {e}")
            return False
