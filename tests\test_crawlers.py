"""
爬虫模块测试

测试data_trans.crawlers模块的功能。
"""

from data_trans.crawlers import *  # noqa: F403,F401


class TestCrawlers:
    """爬虫模块测试类"""

    def test_module_import(self) -> None:
        """测试模块导入"""
        # 基础导入测试
        import src.data_trans.crawlers

        assert src.data_trans.crawlers.__version__ == "0.1.0"

    # TODO: 添加具体的爬虫功能测试
    # def test_base_crawler(self):
    #     pass

    # def test_web_crawler(self):
    #     pass
