# 环境配置示例文件
# 复制此文件为 .env 并根据实际情况修改配置值

# =============================================================================
# 应用基础配置
# =============================================================================
APP_NAME=DataTrans
APP_VERSION=0.1.0
ENVIRONMENT=development
DEBUG=true
HOST=0.0.0.0
PORT=8000
WORKERS=1

# 安全配置
SECRET_KEY=your-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# =============================================================================
# 数据库配置
# =============================================================================

# PostgreSQL配置
DB_POSTGRES_HOST=localhost
DB_POSTGRES_PORT=5432
DB_POSTGRES_USER=postgres
DB_POSTGRES_PASSWORD=password
DB_POSTGRES_DATABASE=data_trans
DB_POSTGRES_POOL_SIZE=10
DB_POSTGRES_MAX_OVERFLOW=20

# MongoDB配置
DB_MONGODB_HOST=localhost
DB_MONGODB_PORT=27017
DB_MONGODB_USER=
DB_MONGODB_PASSWORD=
DB_MONGODB_DATABASE=data_trans
DB_MONGODB_AUTH_SOURCE=admin

# ClickHouse配置
DB_CLICKHOUSE_HOST=localhost
DB_CLICKHOUSE_PORT=9000
DB_CLICKHOUSE_USER=default
DB_CLICKHOUSE_PASSWORD=
DB_CLICKHOUSE_DATABASE=data_trans

# =============================================================================
# Redis配置
# =============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0
REDIS_MAX_CONNECTIONS=100
REDIS_SOCKET_TIMEOUT=5.0
REDIS_CONNECT_TIMEOUT=5.0

# Redis任务队列配置
REDIS_QUEUE_DATABASE=1
REDIS_RESULT_DATABASE=2

# =============================================================================
# 爬虫配置
# =============================================================================
CRAWLER_USER_AGENT=DataTrans/1.0 (+https://github.com/datatrans/crawler)
CRAWLER_TIMEOUT=30.0
CRAWLER_MAX_RETRIES=3
CRAWLER_RETRY_DELAY=1.0
CRAWLER_MAX_CONCURRENT=10
CRAWLER_RATE_LIMIT=1.0

# 代理配置
CRAWLER_PROXY_ENABLED=false
CRAWLER_PROXY_LIST=
CRAWLER_PROXY_ROTATION=true

# JavaScript渲染
CRAWLER_JS_ENABLED=false
CRAWLER_JS_TIMEOUT=10.0

# 下载路径
CRAWLER_DOWNLOAD_PATH=./downloads

# =============================================================================
# Ray分布式计算配置
# =============================================================================
RAY_ADDRESS=
RAY_NUM_CPUS=
RAY_NUM_GPUS=

# =============================================================================
# 开发环境特定配置
# =============================================================================
# 在开发环境中可以启用的额外配置
RELOAD=true
ACCESS_LOG=true
