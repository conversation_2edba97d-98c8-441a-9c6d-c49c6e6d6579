"""
基础爬虫抽象类

定义了所有爬虫必须实现的接口，确保一致性和可扩展性。
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class CrawlResult(BaseModel):
    """爬取结果数据模型"""

    url: str = Field(description="爬取的URL")
    data: Dict[str, Any] = Field(description="爬取到的数据")
    status_code: int = Field(description="HTTP状态码")
    headers: Dict[str, str] = Field(default_factory=dict, description="响应头")
    timestamp: datetime = Field(default_factory=datetime.now, description="爬取时间")
    duration: float = Field(description="爬取耗时（秒）")
    error: Optional[str] = Field(default=None, description="错误信息")


class CrawlerConfig(BaseModel):
    """爬虫配置基类"""

    # 基础配置
    user_agent: str = Field(
        default="DataTrans/1.0 (+https://github.com/datatrans/crawler)",
        description="用户代理",
    )
    timeout: float = Field(default=30.0, description="请求超时时间")
    max_retries: int = Field(default=3, description="最大重试次数")
    retry_delay: float = Field(default=1.0, description="重试延迟")

    # 并发控制
    max_concurrent_requests: int = Field(default=10, description="最大并发请求数")
    rate_limit_per_second: float = Field(default=1.0, description="每秒请求限制")

    # 代理配置
    proxy_enabled: bool = Field(default=False, description="是否启用代理")
    proxy_list: List[str] = Field(default_factory=list, description="代理列表")
    proxy_rotation: bool = Field(default=True, description="是否轮换代理")

    # 请求头配置
    default_headers: Dict[str, str] = Field(
        default_factory=lambda: {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
        },
        description="默认请求头",
    )


class BaseCrawler(ABC):
    """爬虫基类

    所有爬虫实现都必须继承此类并实现抽象方法。
    """

    def __init__(self, config: CrawlerConfig) -> None:
        """初始化爬虫

        Args:
            config: 爬虫配置
        """
        self.config = config
        self._session: Optional[Any] = None
        self._semaphore = asyncio.Semaphore(config.max_concurrent_requests)
        self._rate_limiter = asyncio.Semaphore(1)
        self._last_request_time = 0.0
        self._proxy_index = 0

    @abstractmethod
    async def setup(self) -> None:
        """设置爬虫（如创建会话等）"""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """清理爬虫资源"""
        pass

    @abstractmethod
    async def fetch_single(self, url: str, **kwargs: Any) -> CrawlResult:
        """爬取单个URL

        Args:
            url: 要爬取的URL
            **kwargs: 额外参数

        Returns:
            爬取结果
        """
        pass

    async def fetch_multiple(self, urls: List[str], **kwargs: Any) -> List[CrawlResult]:
        """批量爬取URL

        Args:
            urls: 要爬取的URL列表
            **kwargs: 额外参数

        Returns:
            爬取结果列表
        """
        tasks = []
        for url in urls:
            task = asyncio.create_task(self._fetch_with_semaphore(url, **kwargs))
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                error_result = CrawlResult(
                    url=urls[i], data={}, status_code=0, duration=0.0, error=str(result)
                )
                processed_results.append(error_result)
            elif isinstance(result, CrawlResult):
                processed_results.append(result)

        return processed_results

    async def _fetch_with_semaphore(self, url: str, **kwargs: Any) -> CrawlResult:
        """使用信号量控制并发的爬取方法

        Args:
            url: 要爬取的URL
            **kwargs: 额外参数

        Returns:
            爬取结果
        """
        async with self._semaphore:
            await self._rate_limit()
            return await self._fetch_with_retry(url, **kwargs)

    async def _rate_limit(self) -> None:
        """速率限制"""
        async with self._rate_limiter:
            current_time = asyncio.get_event_loop().time()
            time_since_last = current_time - self._last_request_time
            min_interval = 1.0 / self.config.rate_limit_per_second

            if time_since_last < min_interval:
                await asyncio.sleep(min_interval - time_since_last)

            self._last_request_time = asyncio.get_event_loop().time()

    async def _fetch_with_retry(self, url: str, **kwargs: Any) -> CrawlResult:
        """带重试的爬取方法

        Args:
            url: 要爬取的URL
            **kwargs: 额外参数

        Returns:
            爬取结果
        """
        last_exception = None

        for attempt in range(self.config.max_retries + 1):
            try:
                if attempt > 0:
                    delay = self.config.retry_delay * (2 ** (attempt - 1))  # 指数退避
                    await asyncio.sleep(delay)
                    logger.info(f"重试爬取 {url}，第 {attempt} 次尝试")

                return await self.fetch_single(url, **kwargs)

            except Exception as e:
                last_exception = e
                logger.warning(f"爬取 {url} 失败（第 {attempt + 1} 次尝试）: {e}")

        # 所有重试都失败了
        return CrawlResult(
            url=url,
            data={},
            status_code=0,
            duration=0.0,
            error=f"重试 {self.config.max_retries} 次后仍然失败: {last_exception}",
        )

    def _get_proxy(self) -> Optional[str]:
        """获取代理地址

        Returns:
            代理地址，如果没有配置代理则返回None
        """
        if not self.config.proxy_enabled or not self.config.proxy_list:
            return None

        if self.config.proxy_rotation:
            proxy = self.config.proxy_list[self._proxy_index]
            self._proxy_index = (self._proxy_index + 1) % len(self.config.proxy_list)
            return proxy
        else:
            return self.config.proxy_list[0]

    def _get_headers(
        self, additional_headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, str]:
        """获取请求头

        Args:
            additional_headers: 额外的请求头

        Returns:
            完整的请求头字典
        """
        headers = self.config.default_headers.copy()
        headers["User-Agent"] = self.config.user_agent

        if additional_headers:
            headers.update(additional_headers)

        return headers

    async def __aenter__(self) -> "BaseCrawler":
        """异步上下文管理器入口"""
        await self.setup()
        return self

    async def __aexit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        """异步上下文管理器出口"""
        await self.cleanup()

    def __repr__(self) -> str:
        """字符串表示"""
        return (
            f"{self.__class__.__name__}"
            f"(max_concurrent={self.config.max_concurrent_requests})"
        )


class CrawlerStats(BaseModel):
    """爬虫统计信息"""

    total_requests: int = Field(default=0, description="总请求数")
    successful_requests: int = Field(default=0, description="成功请求数")
    failed_requests: int = Field(default=0, description="失败请求数")
    total_duration: float = Field(default=0.0, description="总耗时")
    average_duration: float = Field(default=0.0, description="平均耗时")
    start_time: Optional[datetime] = Field(default=None, description="开始时间")
    end_time: Optional[datetime] = Field(default=None, description="结束时间")

    def update_from_result(self, result: CrawlResult) -> None:
        """从爬取结果更新统计信息

        Args:
            result: 爬取结果
        """
        self.total_requests += 1
        self.total_duration += result.duration

        if result.error is None and 200 <= result.status_code < 300:
            self.successful_requests += 1
        else:
            self.failed_requests += 1

        if self.total_requests > 0:
            self.average_duration = self.total_duration / self.total_requests

    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests

    @property
    def failure_rate(self) -> float:
        """失败率"""
        return 1.0 - self.success_rate
