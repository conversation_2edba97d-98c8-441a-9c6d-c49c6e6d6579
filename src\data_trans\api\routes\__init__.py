"""
API路由模块

包含所有API路由的组织和导出。
"""

from typing import Any, Dict

from fastapi import APIRouter

from .cleaners import router as cleaners_router
from .crawlers import router as crawlers_router
from .storage import router as storage_router

# 创建主API路由器
api_router = APIRouter(prefix="/api/v1")

# 包含所有子路由
api_router.include_router(crawlers_router)
api_router.include_router(cleaners_router)
api_router.include_router(storage_router)


# 添加基本的API信息端点
@api_router.get("/")  # type: ignore[misc]
async def api_info() -> Dict[str, Any]:
    """API信息端点"""
    return {
        "name": "DataTrans API",
        "version": "v1",
        "description": "分布式数据收集、清洗和存储系统API",
        "endpoints": {
            "crawlers": "/api/v1/crawlers",
            "cleaners": "/api/v1/cleaners",
            "storage": "/api/v1/storage",
        },
    }


# 导出路由器
__all__ = ["api_router", "crawlers_router", "cleaners_router", "storage_router"]
