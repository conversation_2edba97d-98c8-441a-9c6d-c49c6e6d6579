{"mcpServers": {"task-master-ai": {"command": "npx", "args": ["-y", "task-master-ai"], "env": {"ANTHROPIC_API_KEY": "ANTHROPIC_API_KEY_HERE", "PERPLEXITY_API_KEY": "PERPLEXITY_API_KEY_HERE", "OPENAI_API_KEY": "OPENAI_API_KEY_HERE", "GOOGLE_API_KEY": "GOOGLE_API_KEY_HERE", "XAI_API_KEY": "XAI_API_KEY_HERE", "OPENROUTER_API_KEY": "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", "MISTRAL_API_KEY": "MISTRAL_API_KEY_HERE", "AZURE_OPENAI_API_KEY": "AZURE_OPENAI_API_KEY_HERE", "OLLAMA_API_KEY": "OLLAMA_API_KEY_HERE"}, "alwaysAllow": ["models", "rules", "parse_prd", "analyze_project_complexity", "expand_task", "expand_all", "get_task", "complexity_report", "set_task_status", "generate", "add_task", "add_subtask", "update", "update_task", "update_subtask", "remove_task", "remove_subtask", "clear_subtasks", "move_task", "add_dependency", "remove_dependency", "research", "copy_tag", "rename_tag", "use_tag", "delete_tag", "add_tag", "list_tags", "response-language", "fix_dependencies", "validate_dependencies", "next_task", "get_tasks"], "timeout": 30}}}