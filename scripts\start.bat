@echo off
REM DataTrans 分布式数据传输系统启动脚本 (Windows)
REM 用于启动所有Docker服务

echo 启动 DataTrans 分布式数据传输系统...

REM 检查Docker是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: Docker 未运行，请先启动 Docker
    pause
    exit /b 1
)

REM 检查docker-compose是否可用
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: docker-compose 未安装
    pause
    exit /b 1
)

REM 检查.env文件是否存在
if not exist ".env" (
    echo ❌ 错误: .env 文件不存在
    pause
    exit /b 1
)

echo 📋 检查配置文件...

REM 验证Docker Compose配置
echo 🔍 验证 Docker Compose 配置...
docker-compose config >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: Docker Compose 配置无效
    pause
    exit /b 1
)

echo ✅ 配置验证通过

REM 创建网络（如果不存在）
echo 🌐 创建网络...
docker-compose up --no-start

REM 启动基础设施服务（按依赖顺序）
echo 🗄️  启动数据库服务...
docker-compose up -d postgres mongodb clickhouse

echo ⏳ 等待数据库服务启动...
timeout /t 10 /nobreak >nul

echo 🔄 启动缓存和消息队列服务...
docker-compose up -d zookeeper
timeout /t 5 /nobreak >nul
docker-compose up -d redis kafka

echo ⏳ 等待缓存和消息队列服务启动...
timeout /t 10 /nobreak >nul

echo 📦 启动对象存储服务...
docker-compose up -d minio

echo ⏳ 等待对象存储服务启动...
timeout /t 5 /nobreak >nul

REM 检查所有服务状态
echo 🔍 检查服务状态...
docker-compose ps

echo.
echo 🎉 DataTrans 系统启动完成！
echo.
echo 📊 服务访问地址:
echo   PostgreSQL:     localhost:5432
echo   MongoDB:        localhost:27017
echo   ClickHouse:     localhost:8123 (HTTP), localhost:9000 (Native)
echo   Redis:          localhost:6379
echo   Kafka:          localhost:9092
echo   Zookeeper:      localhost:2181
echo   MinIO API:      localhost:9002
echo   MinIO Console:  localhost:9001
echo.
echo 🔑 默认凭据:
echo   PostgreSQL:     用户: data_trans_user, 密码: data_trans_password
echo   MongoDB:        用户: admin, 密码: admin_password
echo   ClickHouse:     用户: data_trans_user, 密码: data_trans_password
echo   MinIO:          用户: data_trans_admin, 密码: data_trans_password123
echo.
echo 📝 查看日志: docker-compose logs -f [服务名]
echo 🛑 停止系统: scripts\stop.bat
echo.
pause
